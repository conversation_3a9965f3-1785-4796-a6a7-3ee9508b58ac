﻿// <copyright file="LegalEntitiesAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.LegalEntities;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Models;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.BeneficialOwners;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Shareholders;
using NetProGroup.Trust.DataManager.Jurisdictions;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.LegalEntities.RequestResponses;
using NetProGroup.Trust.DataManager.LegalEntityRelations;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Shared.Enums;
using NetProGroup.Trust.Domain.Shared.Permissions;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.LegalEntities
{
    /// <summary>
    /// Application service for legal entities.
    /// </summary>
    public class LegalEntitiesAppService : ILegalEntitiesAppService
    {
        private readonly IWorkContext _workContext;
        private readonly ILegalEntitiesDataManager _dataManager;
        private readonly IBeneficialOwnersDataManager _beneficialOwnersDataManager;
        private readonly IDirectorsDataManager _directorsDataManager;
        private readonly IShareholdersDataManager _shareholdersDataManager;
        private readonly ISecurityManager _securityManager;
        private readonly IJurisdictionsDataManager _jurisdictionsDataManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="LegalEntitiesAppService"/> class.
        /// </summary>
        /// <param name="workContext">The work context.</param>
        /// <param name="dataManager">The DataManager to use.</param>
        /// <param name="beneficialOwnersDataManager">The datamanager for benficial owners.</param>
        /// <param name="directorsDataManager">The datamanager for directors.</param>
        /// <param name="shareholdersDataManager">The datamanager for shareholders.</param>
        /// <param name="securityManager">The security manager.</param>
        /// <param name="jurisdictionsDataManager">The datamanager for jurisdictions.</param>
        public LegalEntitiesAppService(IWorkContext workContext,
            ILegalEntitiesDataManager dataManager,
            IBeneficialOwnersDataManager beneficialOwnersDataManager,
            IDirectorsDataManager directorsDataManager,
            IShareholdersDataManager shareholdersDataManager,
            ISecurityManager securityManager,
            IJurisdictionsDataManager jurisdictionsDataManager)
        {
            _workContext = workContext;
            _dataManager = dataManager;
            _securityManager = securityManager;
            _jurisdictionsDataManager = jurisdictionsDataManager;
            _beneficialOwnersDataManager = beneficialOwnersDataManager;
            _directorsDataManager = directorsDataManager;
            _shareholdersDataManager = shareholdersDataManager;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<CompanyDTO>> ListCompaniesAsync(Guid? masterClientId,
            string searchTerm,
            bool? active,
            List<OnboardingStatus> onboardingStatuses,
            int pageNumber,
            int pageSize,
            string sortBy,
            string sortOrder)
        {
            // Authorization
            const string requiredPermission = WellKnownPermissionNames.Companies_Search;
            var authorizedJurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(requiredPermission);

            var response = await _dataManager.ListCompaniesAsync(new ListCompaniesRequest
            {
                MasterClientId = masterClientId,
                SearchTerm = searchTerm,
                Active = active,
                OnboardingStatuses = onboardingStatuses,
                PageNumber = pageNumber,
                PageSize = pageSize,
                AuthorizedJurisdictionIDs = authorizedJurisdictionIDs,
                SortBy = sortBy,
                SortOrder = sortOrder
            });
            return response.CompanyItems;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<BeneficialOwnerDTO>> ListBeneficialOwnersAsync(Guid legalEntityId, bool includeMetaData, int pageNumber, int pageSize)
        {
            await _securityManager.RequireClientAccessToCompanyAsync(legalEntityId);

            var request = new DataManager.LegalEntityRelations.BeneficialOwners.RequestResponses.ListBeneficialOwnersRequest
            {
                LegalEntityId = legalEntityId,
                IncludeMetaData = includeMetaData,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
            var response = await _beneficialOwnersDataManager.ListBeneficialOwnersAsync(request);

            return response.BeneficialOwnerItems;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<DirectorDTO>> ListDirectorsAsync(Guid legalEntityId, bool includeMetaData, int pageNumber, int pageSize)
        {
            await _securityManager.RequireClientAccessToCompanyAsync(legalEntityId);

            var request = new DataManager.LegalEntityRelations.Directors.RequestResponses.ListDirectorsRequest
            {
                LegalEntityId = legalEntityId,
                IncludeMetaData = includeMetaData,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
            var response = await _directorsDataManager.ListDirectorsAsync(request);

            return response.DirectorItems;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ShareholderDTO>> ListShareholdersAsync(Guid legalEntityId, int pageNumber, int pageSize)
        {
            await _securityManager.RequireClientAccessToCompanyAsync(legalEntityId);

            var request = new DataManager.LegalEntityRelations.Shareholders.RequestResponses.ListShareholdersRequest
            {
                LegalEntityId = legalEntityId,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
            var response = await _shareholdersDataManager.ListShareholdersAsync(request);

            return response.ShareholderItems;
        }

        /// <inheritdoc/>
        public async Task RequestAssistanceAsync(RequestAssistanceDTO requestAssistance)
        {
            ArgumentNullException.ThrowIfNull(requestAssistance, nameof(requestAssistance));

            await _securityManager.RequireClientAccessToCompanyAsync(requestAssistance.LegalEntityId);

            var request = new DataManager.LegalEntityRelations.RequestResponses.RequestAssistanceRequest
            {
                LegalEntityId = requestAssistance.LegalEntityId,
                UserId = _workContext.IdentityUserId.Value,
                AssistanceRequestType = requestAssistance.AssistanceRequestType,
                AssistanceRequestComments = requestAssistance.AssistanceRequestComments
            };

            switch (requestAssistance.AssistanceRequestType)
            {
                case DomainShared.Enums.LegalEntityRelationAssistanceRequestType.NoBeneficialOwner:
                    {
                        await _beneficialOwnersDataManager.RequestAssistanceAsync(request);
                        break;
                    }

                case DomainShared.Enums.LegalEntityRelationAssistanceRequestType.NoDirector:
                    {
                        await _directorsDataManager.RequestAssistanceAsync(request);
                        break;
                    }

                case DomainShared.Enums.LegalEntityRelationAssistanceRequestType.NoShareholder:
                    {
                        await _shareholdersDataManager.RequestAssistanceAsync(request);
                        break;
                    }

                default: throw new ConstraintException("No valid AssistanceRequestType");
            }
        }

        /// <inheritdoc/>
        public async Task<IPagedList<CompanyWithAnnualFeeStatusSearchResultDTO>> SearchCompaniesWithAnnualFeeStatusAsync(int financialYear,
            bool isPaid,
            string searchTerm,
            int pageNumber,
            int pageSize)
        {
            var requiredPermission = WellKnownPermissionNames.Companies_View_Annual_Fee;
            var authorizedJurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(requiredPermission);

            var response = await _dataManager.SearchCompaniesWithAnnualFeeStatusAsync(
                new SearchCompanyWithAnnualFeeStatusRequest
                {
                    FinancialYear = financialYear,
                    IsPaid = isPaid,
                    SearchTerm = searchTerm,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    AuthorizedJurisdictionIDs = authorizedJurisdictionIDs
                });

            return response.CompanyItems;
        }

        /// <inheritdoc/>
        public async Task UpdateCompanyAnnualFeeStatusAsync(List<Guid> legalEntityIds, int financialYear, bool isPaid)
        {
            ArgumentNullException.ThrowIfNull(legalEntityIds, nameof(legalEntityIds));

            var jurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(WellKnownPermissionNames.Companies_Set_Annual_Fee);

            await _dataManager.UpdateCompaniesAnnualFeeStatusAsync(legalEntityIds, financialYear, isPaid, jurisdictionIDs);
        }

        /// <inheritdoc/>
        public async Task<CompanyAnnualFeesDTO> SetCompanyAnnualFeeStatusAsync(SetCompanyAnnualFeesDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            await _securityManager.RequireManagementPermissionForLegalEntityAsync(WellKnownPermissionNames.Companies_Set_Annual_Fee, model.CompanyId);

            await _dataManager.SetCompanyAnnualFeeStatusAsync(model.CompanyId, model.AnnualFees);

            return await GetCompanyAnnualFeeStatusAsync(model.CompanyId);
        }

        /// <inheritdoc/>
        public async Task<CompanyAnnualFeesDTO> GetCompanyAnnualFeeStatusAsync(Guid legalEntityId)
        {
            await _securityManager.RequireManagementPermissionForLegalEntityAsync(WellKnownPermissionNames.Companies_View_Annual_Fee, legalEntityId);

            return await _dataManager.GetCompanyAnnualFeeStatusesAsync(legalEntityId);
        }

        /// <inheritdoc />
        public async Task<CompanyDTO> GetCompanyByIdAsync(Guid companyId)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionForLegalEntityAsync(WellKnownPermissionNames.Companies_View, companyId);

            return await _dataManager.GetCompanyByIdAsync(companyId);
        }

        /// <inheritdoc/>
        public async Task ApproveCompanyAsync(Guid companyId)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionForLegalEntityAsync(WellKnownPermissionNames.Companies_Approve_Onboarding, companyId);

            await _dataManager.ApproveCompanyAsync(companyId);
        }

        /// <inheritdoc/>
        public async Task DeclineCompanyAsync(Guid companyId, string declineReason)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionForLegalEntityAsync(WellKnownPermissionNames.Companies_Reject_Onboarding, companyId);

            await _dataManager.DeclineCompanyAsync(companyId, declineReason);
        }

        /// <inheritdoc/>
        public async Task ResetCompanyToOnboardingAsync(Guid companyId)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionForLegalEntityAsync(WellKnownPermissionNames.Companies_Set_Back_To_Onboarding_No_Submissions, companyId);

            await _dataManager.ResetCompanyToOnboardingAsync(companyId);
        }
    }
}
