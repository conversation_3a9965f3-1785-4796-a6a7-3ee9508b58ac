﻿// <copyright file="CompaniesController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Models.Exceptions;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Communication;
using NetProGroup.Trust.Application.Contracts.LegalEntities;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Models;
using NetProGroup.Trust.Application.Contracts.Modules;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.Companies
{
    /// <summary>
    /// Controller for companies.
    /// </summary>
    [ApiController]
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/[Controller]")]
    public class CompaniesController : TrustAPIControllerBase
    {
        private readonly ILegalEntitiesAppService _legalEntitiesAppService;
        private readonly IModulesAppService _modulesAppService;
        private readonly ISettingsAppService _settingsAppService;
        private readonly ICommunicationAppService _communicationAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="CompaniesController"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="legalEntitiesAppService">The service for legal entities.</param>
        /// <param name="modulesAppService">The service for modules.</param>
        /// <param name="settingsAppService">The appservice for settings.</param>
        /// <param name="communicationAppService">The service for comminication.</param>
        public CompaniesController(
            ILogger<CompaniesController> logger,
            ILegalEntitiesAppService legalEntitiesAppService,
            IModulesAppService modulesAppService,
            ISettingsAppService settingsAppService,
            ICommunicationAppService communicationAppService)
            : base(logger)
        {
            _legalEntitiesAppService = legalEntitiesAppService;
            _modulesAppService = modulesAppService;
            _settingsAppService = settingsAppService;
            _communicationAppService = communicationAppService;
        }

        /// <summary>
        /// Gets the companies with pagination.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/companies?masterclientid={masterclientid}&amp;searchTerm={search}&amp;active=true&amp;onboardingStatus={onboardingStatus}&amp;pageNumber={pageNumber}&amp;pageSize={pageSize}
        /// .
        /// </remarks>
        /// <param name="masterClientId">Optional id of the masterclient.</param>
        /// <param name="searchTerm">Optional search term to filter companies.</param>
        /// <param name="isActive">Optional flag to filter active companies.</param>
        /// <param name="onboardingStatus">Optional onboarding status to filter companies (multiple statuses possible).</param>
        /// <param name="pageNumber">The page number to retrieve. Default is the first page.</param>
        /// <param name="pageSize">The number of items per page. Default is the specified page size.</param>
        /// <param name="sortBy">The field to sort on.</param>
        /// <param name="sortOrder">The order to sort on.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the companies.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "GetCompanies", Summary = "Get companies with pagination.")]
        [ProducesResponseType(typeof(PaginatedResponse<CompanyDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCompanies(
            Guid? masterClientId = null,
            string searchTerm = null,
            bool? isActive = null,
            [FromQuery] List<OnboardingStatus> onboardingStatus = null,
            int pageNumber = PagingSettings.DefaultPageNumber,
            int pageSize = PagingSettings.DefaultPageSize,
            string sortBy = null, string sortOrder = null)
        {
            var result = await ProcessRequestWithPagedResponseAsync(new PagingInfo(pageNumber, pageSize),
                validate: () =>
                {
                    Check.Positive(pageNumber, nameof(pageNumber));
                    Check.Positive(pageSize, nameof(pageSize));

                    if (onboardingStatus != null && onboardingStatus.Contains(OnboardingStatus.ClosedWhileOnboarding))
                    {
                        throw new BadRequestException("OnboardingStatus ClosedWhileOnboarding not allowed");
                    }
                },
                executeAsync: async (pagingInfo) =>
                    await _legalEntitiesAppService.ListCompaniesAsync(masterClientId, searchTerm, isActive, onboardingStatus, pagingInfo.PageNumber,
                        pagingInfo.PageSize, sortBy, sortOrder));

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the list of modules for the given company.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/companies/{companyId}/modules.
        /// </remarks>
        /// <param name="companyId">The id of the company to get the modules for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the modules.</returns>
        [HttpGet("{companyId}/modules")]
        [SwaggerOperation(OperationId = "GetCompanyModules")]
        [ProducesResponseType(typeof(ListCompanyModulesDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCompanyModules(Guid companyId)
        {
            ListCompanyModulesDTO item = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _modulesAppService.GetCompanyModulesAsync(companyId, forClientUI: false);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Set approved and enabled status for a list of modules for the given company.
        /// Only the modules that are in the list will have their status changed.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /api/v1/management/companies/{companyid}/modules.
        /// </remarks>
        /// <param name="companyId">The id of the company to set the modules for.</param>
        /// <param name="setModulesDTO">The model with modules to set.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the modules.</returns>
        [HttpPut("{companyId}/modules")]
        [SwaggerOperation(OperationId = "SetCompanyModules")]
        [ProducesResponseType(typeof(ListCompanyModulesDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> SetCompanyModules(
            Guid companyId,
            SetCompanyModulesDTO setModulesDTO)
        {
            ListCompanyModulesDTO item = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                    Check.NotNull(setModulesDTO, nameof(setModulesDTO));
                },

                executeAsync: async () =>
                {
                    item = await _modulesAppService.SetCompanyModulesAsync(companyId, setModulesDTO.Modules);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets a set of settings.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/management/companies/{companyid}/settings.
        ///
        /// </remarks>
        /// <param name="companyId">Id of the company.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the settings.</returns>
        [HttpGet("{companyId}/settings")]
        [SwaggerOperation(OperationId = "GetCompanySettings")]
        [ProducesResponseType(typeof(SettingsDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCompanySettings(
            Guid companyId)
        {
            SettingsDTO item = null;

            var result = await ProcessRequestAsync(
                executeAsync: async () =>
                {
                    item = await _settingsAppService.ReadSettingsForCompanyAsync(companyId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Posts a set of settings for a specific purpose, identified by the key.
        /// </summary>
        /// <remarks>
        /// Valid values for 'key':
        ///   - fees
        ///   - submissions
        ///
        /// Sample request:
        ///
        ///     POST /api/management/companies/{companyid}/settings/{key}
        ///     {
        ///
        ///     }.
        ///
        /// </remarks>
        /// <param name="companyId">Id of the company.</param>
        /// <param name="key">The key to identify the type of settings.</param>
        /// <param name="data">The payload for the settings.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the settings.</returns>
        [HttpPost("{companyId}/settings/{key}")]
        [SwaggerOperation(OperationId = "SetCompanySettingsByKey")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> SetCompanySettingsByKey(
            Guid companyId,
            string key,
            object data)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotNullOrEmpty(key, nameof(key));
                },

                executeAsync: async () =>
                {
                    await _settingsAppService.SaveSettingsForCompanyAsync(companyId, key, data.ToString());
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Posts a set of settings.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/management/companies/{companyid}/settings
        ///     {
        ///
        ///     }.
        ///
        /// </remarks>
        /// <param name="companyId">Id of the company.</param>
        /// <param name="settings">The settings.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the settings.</returns>
        [HttpPost("{companyId}/settings")]
        [SwaggerOperation(OperationId = "SetCompanySettings")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> SetCompanySettings(
            Guid companyId,
            SettingsDTO settings)
        {
            var result = await ProcessRequestAsync<object>(
                executeAsync: async () =>
                {
                    await _settingsAppService.SaveSettingsForCompanyAsync(companyId, settings);
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Gets the annual fee statuses for companies with pagination.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     GET /api/v1/management/companies/annual-fee-status?financialYear={financialYear}&amp;isPaid={isPaid}&amp;searchTerm={searchTerm}&amp;pageNumber={pageNumber}&amp;pageSize={pageSize}.
        /// </remarks>
        /// <param name="financialYear">The financial year for which to retrieve the annual fee statuses.</param>
        /// <param name="isPaid">Parameter to filter by paid status. True for paid, false for unpaid.</param>
        /// <param name="searchTerm">Optional search term to filter the results.</param>
        /// <param name="pageNumber">The page number to retrieve. Default is the first page.</param>
        /// <param name="pageSize">The number of items per page. Default is the specified page size.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the company annual fee statuses.</returns>
        [HttpGet("annual-fee-status")]
        [SwaggerOperation(OperationId = "GetCompanyAnnualFees", Summary = "Get company annual fee status with pagination.")]
        [ProducesResponseType(typeof(PaginatedResponse<CompanyWithAnnualFeeStatusSearchResultDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCompanyAnnualFees(
            int financialYear,
            bool isPaid,
            string searchTerm = null,
            int pageNumber = PagingSettings.DefaultPageNumber,
            int pageSize = PagingSettings.DefaultPageSize)
        {
            var result = await ProcessRequestWithPagedResponseAsync(new PagingInfo(pageNumber, pageSize),
                validate: () =>
                {
                    Check.Positive(pageNumber, nameof(pageNumber));
                    Check.Positive(pageSize, nameof(pageSize));
                    Check.Positive(financialYear, nameof(financialYear));
                },
                executeAsync: async (pagingInfo) =>
                    await _legalEntitiesAppService.SearchCompaniesWithAnnualFeeStatusAsync(
                        financialYear,
                        isPaid,
                        searchTerm,
                        pagingInfo.PageNumber,
                        pagingInfo.PageSize));

            return result.AsResponse();
        }

        /// <summary>
        /// Sets the annual fee status for multiple companies.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     PUT /api/v1/management/companies/annual-fee-status
        ///     {
        ///         "financialYear": 2023,
        ///         "isPaid": true,
        ///         "legalEntityIds": ["guid1", "guid2", "guid3"]
        ///     }.
        /// </remarks>
        /// <param name="updateDto">The DTO containing the update information.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPut("annual-fee-status")]
        [SwaggerOperation(OperationId = "SetCompanyAnnualFeeStatus", Summary = "Set company annual fee status for multiple companies.")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SetCompanyAnnualFeeStatus(
            [FromBody] UpdateCompanyAnnualFeeStatusDTO updateDto)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotNull(updateDto, nameof(updateDto));
                    Check.Positive(updateDto.FinancialYear, nameof(updateDto.FinancialYear));
                    Check.NotNullOrEmpty(updateDto.LegalEntityIds, nameof(updateDto.LegalEntityIds));
                },
                executeAsync: async () =>
                {
                    await _legalEntitiesAppService.UpdateCompanyAnnualFeeStatusAsync(updateDto.LegalEntityIds, updateDto.FinancialYear, updateDto.IsPaid);
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Sets the annual fee status for multiple financial years for a single company.
        /// </summary>
        /// <remarks>
        /// Only financial years that are listed are updated.
        ///
        /// Sample request:
        ///     PUT /api/v1/management/companies/{companyId}/annual-fee-status
        ///     {
        ///
        ///     }.
        /// </remarks>
        /// <param name="companyId">The id of the compnay to set the annual fees for.</param>
        /// <param name="model">The DTO containing the update information.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPut("{companyId}/annual-fee-status")]
        [SwaggerOperation(OperationId = "SetCompanyAnnualFeeStatusByCompany", Summary = "Sets the annual fee status for multiple financial years for a single company.")]
        [ProducesResponseType(typeof(CompanyAnnualFeesDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SetCompanyAnnualFeeStatus(
            [FromRoute]
            Guid companyId,
            [FromBody] SetCompanyAnnualFeesDTO model)
        {
            CompanyAnnualFeesDTO item = null;

            var result = await ProcessRequestAsync<CompanyAnnualFeesDTO>(
                validate: () =>
                {
                    Check.NotNull(model, nameof(model));
                    Check.NotDefaultOrNull<Guid>(companyId, nameof(companyId));

                    model.CompanyId = companyId;
                },

                executeAsync: async () =>
                {
                    item = await _legalEntitiesAppService.SetCompanyAnnualFeeStatusAsync(model);
                },

                createResponseModel: () => item);

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the annual fee status for multiple financial years for a single company.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     GET /api/v1/management/companies/{companyId}/annual-fee-status.
        /// </remarks>
        /// <param name="companyId">The id of the compnay to get the annual fees for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpGet("{companyId}/annual-fee-status")]
        [SwaggerOperation(OperationId = "GetCompanyAnnualFeeStatusByCompany", Summary = "Gets the annual fee status for multiple financial years for a single company.")]
        [ProducesResponseType(typeof(CompanyAnnualFeesDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetCompanyAnnualFeeStatus(
            [FromRoute]
            Guid companyId)
        {
            CompanyAnnualFeesDTO item = null;

            var result = await ProcessRequestAsync<CompanyAnnualFeesDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(companyId, nameof(companyId));
                },

                executeAsync: async () =>
                {
                    item = await _legalEntitiesAppService.GetCompanyAnnualFeeStatusAsync(companyId);
                },

                createResponseModel: () => item);

            return result.AsResponse();
        }

        /// <summary>
        /// Sends an email related to the company.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     POST /api/v1/management/companies/{companyId}/email
        ///     {
        ///         "RecipientEmailAddress": "",
        ///         "Subject": "",
        ///         "Body": "",
        ///     }.
        /// </remarks>
        /// <param name="companyId">The id of the company thet the email is related to.</param>
        /// <param name="model">The DTO holding the parameters for the new email message.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPost("{companyId}/email")]
        [SwaggerOperation(OperationId = "SendCompanyEmailMessage", Summary = "Sends an email.")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> SendEmailMessage(Guid companyId, SendEmailDTO model)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(companyId, nameof(companyId));
                    Check.NotNullOrEmpty(model.RecipientEmailAddress, nameof(model.RecipientEmailAddress));
                    Check.NotNullOrEmpty(model.Subject, nameof(model.Subject));
                    Check.NotNullOrEmpty(model.Body, nameof(model.Body));

                    model.LegalEntityId = companyId;
                },
                executeAsync: async () =>
                {
                    await _communicationAppService.SendEmailAsync(model);
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Gets a single company by its ID.
        /// </summary>
        /// <remarks>
        /// Sample request:\
        ///     GET /api/v1/management/companies/{companyId}
        ///
        /// Possible values for OnboardingStatus:\
        ///   0 = Onboarding\
        ///   1 = Approved\
        ///   2 = Declined\
        /// .
        /// </remarks>
        /// <param name="companyId">The ID of the company to retrieve.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the company.</returns>
        [HttpGet("{companyId}")]
        [SwaggerOperation(OperationId = "GetCompanyById", Summary = "Get a single company by its ID.")]
        [ProducesResponseType(typeof(CompanyDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetCompanyById(Guid companyId)
        {
            CompanyDTO item = null;
            var result = await ProcessRequestAsync(
                executeAsync: async () =>
                {
                    item = await _legalEntitiesAppService.GetCompanyByIdAsync(companyId);
                },

                createResponseModel: () => item);

            return result.AsResponse();
        }

        /// <summary>
        /// Approves a company's onboarding.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     POST /api/v1/management/companies/{companyId}/approve
        /// .
        /// </remarks>
        /// <param name="companyId">The ID of the company to approve.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [HttpPost("{companyId}/approve")]
        [SwaggerOperation(OperationId = "ApproveCompany", Summary = "Approve a company's onboarding.")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed, Type = typeof(APIExceptionModel))]
        public async Task<IActionResult> ApproveCompany(Guid companyId)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(companyId, nameof(companyId));
                },
                executeAsync: async () =>
                {
                    await _legalEntitiesAppService.ApproveCompanyAsync(companyId);
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Declines a company's onboarding.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     POST /api/v1/management/companies/{companyId}/decline
        ///     {
        ///         "declineReason": "Insufficient documentation provided"
        ///     }
        /// .
        /// </remarks>
        /// <param name="companyId">The ID of the company to decline.</param>
        /// <param name="declineDto">The DTO containing the decline reason.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [HttpPost("{companyId}/decline")]
        [SwaggerOperation(OperationId = "DeclineCompany", Summary = "Decline a company's onboarding.")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeclineCompany(Guid companyId, [FromBody] DeclineCompanyDTO declineDto)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(companyId, nameof(companyId));
                    Check.NotNull(declineDto, nameof(declineDto));
                },
                executeAsync: async () =>
                {
                    await _legalEntitiesAppService.DeclineCompanyAsync(companyId, declineDto.DeclineReason);
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Set the onboarding status of an approved company back to onboarding.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /api/v1/management/companies/{companyId}/reset-to-onboarding
        /// .
        /// </remarks>
        /// <param name="companyId">The ID of the company to reset the status back to onboarding.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [HttpPut("{companyId}/reset-to-onboarding")]
        [SwaggerOperation(OperationId = "ResetCompanyStatusToOnboarding", Summary = "Set the onboarding status of an approved company back to onboarding.")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed, Type = typeof(APIExceptionModel))]
        public async Task<IActionResult> ResetCompanyStatusToOnboarding(Guid companyId)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(companyId, nameof(companyId));
                },
                executeAsync: async () =>
                {
                    await _legalEntitiesAppService.ResetCompanyToOnboardingAsync(companyId);
                });

            return result.AsNoContentResponse();
        }
    }
}
