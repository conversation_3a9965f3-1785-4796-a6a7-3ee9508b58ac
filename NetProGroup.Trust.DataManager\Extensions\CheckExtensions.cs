// <copyright file="CheckExtensions.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.EF.Repository.Interfaces;
using System.Diagnostics.CodeAnalysis;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Domain.Currencies;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Inboxes;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Framework.Services.Documents;
using NetProGroup.Framework.Services.Documents.Models;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Report;
using NetProGroup.Framework.Services.Communication.EFModels;
using NetProGroup.Framework.Services.Communication.EFRepository;
using NetProGroup.Trust.Domain.Announcements;

namespace NetProGroup.Trust.DataManager.Extensions
{
    /// <summary>
    /// Extension methods for checking existence of entities.
    /// </summary>
    public static class CheckExtensions
    {
        /// <summary>
        /// Extension method for checking a user given its id.
        /// </summary>
        /// <param name="userManager">Usermanager to check on.</param>
        /// <param name="userId">User id as Guid.</param>
        /// <param name="throwError">Determine whether if an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether if the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The ApplicationUserDTO entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<ApplicationUserDTO> CheckUserByIdAsync([NotNull] this IUserManager userManager, Guid userId, bool throwError = true, bool throwNotFound = true)
        {
            Check.NotNull(userManager, nameof(userManager));

            var user = await userManager.GetUserByIdAsync(userId);

            if (user == null && throwError)
            {
                var text = FormatNotFoundText("user", userId);
                var code = ErrorEnum.USER_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return user;
        }

        /// <summary>
        /// Extension method for checking a jurisdiction given its id.
        /// </summary>
        /// <param name="jurisdictionsRepository">Repository to check on.</param>
        /// <param name="jurisdictionId">Jurisdiction id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <param name="options">Optional options to apply to the query.</param>
        /// <returns>The Jurisdiction entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<Jurisdiction> CheckJurisdictionByIdAsync([NotNull] this IJurisdictionsRepository jurisdictionsRepository, Guid jurisdictionId, bool throwError = true, bool throwNotFound = true, Func<IQueryable<Jurisdiction>, IQueryable<Jurisdiction>> options = null)
        {
            Check.NotNull(jurisdictionsRepository, nameof(jurisdictionsRepository));

            var jurisdiction = await jurisdictionsRepository.GetByIdAsync(jurisdictionId, options);

            if (jurisdiction == null && throwError)
            {
                var text = FormatNotFoundText("jurisdiction", jurisdictionId);
                var code = ApplicationErrors.JURISDICTION_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return jurisdiction;
        }

        /// <summary>
        /// Extension method for checking a invoice given its id.
        /// </summary>
        /// <param name="invoicesRepository">Repository to check on.</param>
        /// <param name="invoiceId">Invoice id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The Invoice entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<Invoice> CheckInvoiceByIdAsync([NotNull] this IInvoiceRepository invoicesRepository, Guid invoiceId, bool throwError = true, bool throwNotFound = true)
        {
            Check.NotNull(invoicesRepository, nameof(invoicesRepository));

            var invoice = await invoicesRepository.GetByIdAsync(invoiceId);

            if (invoice == null && throwError)
            {
                var text = FormatNotFoundText("invoice", invoiceId);
                var code = ApplicationErrors.INVOICE_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                    new BadRequestException(code, text);
            }

            return invoice;
        }

        /// <summary>
        /// Extension method for checking a report given its id.
        /// </summary>
        /// <param name="repository">Repository to check on.</param>
        /// <param name="reportId">Report id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The Report entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        /// <exception cref="BadRequestException">If not found and throwError == false.</exception>
        public static async Task<Domain.Report.Report> CheckReportByIdAsync([NotNull] this IReportRepository repository,
            Guid reportId,
            bool throwError = true,
            bool throwNotFound = true)
        {
            Check.NotNull(repository, nameof(repository));
            var report = await repository.GetByIdAsync(reportId);
            if (report == null && throwError)
            {
                var text = FormatNotFoundText("report", reportId);
                var code = ApplicationErrors.REPORT_NOT_FOUND.ToErrorCode();
                throw throwNotFound ? new NotFoundException(code, text) : new BadRequestException(code, text);
            }

            return report;
        }

        /// <summary>
        /// Extension method for checking a beneficial owner given its id.
        /// </summary>
        /// <param name="beneficialOwnersRepository">Repository to check on.</param>
        /// <param name="beneficialOwnerId">BeneficialOwner id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The BeneficialOwner entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<BeneficialOwner> CheckBeneficialOwnerByIdAsync([NotNull] this IBeneficialOwnersRepository beneficialOwnersRepository,
            Guid beneficialOwnerId,
            bool throwError = true,
            bool throwNotFound = true)
        {
            Check.NotNull(beneficialOwnersRepository, nameof(beneficialOwnersRepository));
            var beneficialOwner = await beneficialOwnersRepository.GetByIdAsync(beneficialOwnerId);
            if (beneficialOwner == null && throwError)
            {
                var text = FormatNotFoundText("beneficial owner", beneficialOwnerId);
                var code = ApplicationErrors.BENEFICIALOWNER_NOT_FOUND.ToErrorCode();
                throw throwNotFound ? new NotFoundException(code, text) : new BadRequestException(code, text);
            }

            return beneficialOwner;
        }

        /// <summary>
        /// Extension method for checking a jurisdiction given its code.
        /// </summary>
        /// <param name="jurisdictionsRepository">Repository to check on.</param>
        /// <param name="code">Jurisdiction code as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The Jurisdiction entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<Jurisdiction> CheckJurisdictionByCodeAsync([NotNull] this IJurisdictionsRepository jurisdictionsRepository, string code, bool throwError = true, bool throwNotFound = true)
        {
            Check.NotNull(jurisdictionsRepository, nameof(jurisdictionsRepository));

            var jurisdiction = await jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == code);

            if (jurisdiction == null && throwError)
            {
                var text = FormatNotFoundText("jurisdiction", code);
                var errorCode = ApplicationErrors.JURISDICTION_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return jurisdiction;
        }

        /// <summary>
        /// Extension method for checking a director given its id.
        /// </summary>
        /// <param name="directorsRepository">Repository to check on.</param>
        /// <param name="directorId">Director id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The Director entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        /// <exception cref="BadRequestException">If not found and throwError == false.</exception>
        public static async Task<Director> CheckDirectorByIdAsync([NotNull] this IDirectorsRepository directorsRepository,
            Guid directorId,
            bool throwError = true,
            bool throwNotFound = true)
        {
            Check.NotNull(directorsRepository, nameof(directorsRepository));
            var director = await directorsRepository.GetByIdAsync(directorId);
            if (director == null && throwError)
            {
                var text = FormatNotFoundText("director", directorId);
                var code = ApplicationErrors.DIRECTOR_NOT_FOUND.ToErrorCode();
                throw throwNotFound ? new NotFoundException(code, text) : new BadRequestException(code, text);
            }

            return director;
        }

        /// <summary>
        /// Extension method for checking a legal entity given its id.
        /// </summary>
        /// <param name="legalEntitiesRepository">Repository to check on.</param>
        /// <param name="legalEntityId">Jurisdiction id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <param name="options">Optional options to apply to the query.</param>
        /// <returns>The LegalEntity entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<LegalEntity> CheckLegalEntityByIdAsync([NotNull] this ILegalEntitiesRepository legalEntitiesRepository, Guid legalEntityId, bool throwError = true, bool throwNotFound = true, Func<IQueryable<LegalEntity>, IQueryable<LegalEntity>> options = null)
        {
            Check.NotNull(legalEntitiesRepository, nameof(legalEntitiesRepository));

            var legalEntity = await legalEntitiesRepository.GetByIdAsync(legalEntityId, options);

            if (legalEntity == null && throwError)
            {
                var text = FormatNotFoundText("company", legalEntityId);
                var code = ApplicationErrors.COMPANY_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return legalEntity;
        }

        /// <summary>
        /// Extension method for checking a masterclient given its id.
        /// </summary>
        /// <param name="masterClientsRepository">Repository to check on.</param>
        /// <param name="masterClientId">MasterClient id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <param name="options">Optional options to apply to the query.</param>
        /// <returns>The MasterClient entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<MasterClient> CheckMasterClientByIdAsync([NotNull] this IMasterClientsRepository masterClientsRepository, Guid masterClientId, bool throwError = true, bool throwNotFound = true, Func<IQueryable<MasterClient>, IQueryable<MasterClient>> options = null)
        {
            Check.NotNull(masterClientsRepository, nameof(masterClientsRepository));

            var masterClient = await masterClientsRepository.GetByIdAsync(masterClientId, options);

            if (masterClient == null && throwError)
            {
                var text = FormatNotFoundText("masterclient", masterClientId);
                var code = ApplicationErrors.MASTERCLIENT_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return masterClient;
        }

        /// <summary>
        /// Extension method for checking a masterclient given its code.
        /// </summary>
        /// <param name="masterClientsRepository">Repository to check on.</param>
        /// <param name="masterClientCode">MasterClient code as string.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <param name="options">Optional options to apply to the query.</param>
        /// <returns>The MasterClient entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<MasterClient> CheckMasterClientByCodeAsync([NotNull] this IMasterClientsRepository masterClientsRepository, string masterClientCode, bool throwError = true, bool throwNotFound = true, Func<IQueryable<MasterClient>, IQueryable<MasterClient>> options = null)
        {
            Check.NotNull(masterClientsRepository, nameof(masterClientsRepository));

            var masterClient = await masterClientsRepository.FindFirstOrDefaultByConditionAsync(
                mc => mc.Code == masterClientCode, options);

            if (masterClient == null && throwError)
            {
                var text = FormatNotFoundText("masterclient", masterClientCode, "code");
                var code = ApplicationErrors.MASTERCLIENT_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return masterClient;
        }

        /// <summary>
        /// Extension method for checking a module given its id.
        /// </summary>
        /// <param name="modulesRepository">Repository to check on.</param>
        /// <param name="moduleId">Module id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The Module entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<Module> CheckModuleByIdAsync([NotNull] this IModulesRepository modulesRepository, Guid moduleId, bool throwError = true, bool throwNotFound = true)
        {
            Check.NotNull(modulesRepository, nameof(modulesRepository));

            var module = await modulesRepository.GetByIdAsync(moduleId);

            if (module == null && throwError)
            {
                var text = FormatNotFoundText("module", moduleId);
                var code = ApplicationErrors.MODULE_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return module;
        }

        /// <summary>
        /// Extension method for checking a jurisdiction tax rate given its id.
        /// </summary>
        /// <param name="taxRatesRepository">Repository to check on.</param>
        /// <param name="taxRateId">Tax rate id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The JurisdictionTaxRate entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<JurisdictionTaxRate> CheckTaxRateByIdAsync([NotNull] this IJurisdictionTaxRatesRepository taxRatesRepository, Guid taxRateId, bool throwError = true, bool throwNotFound = true)
        {
            Check.NotNull(taxRatesRepository, nameof(taxRatesRepository));

            var taxRate = await taxRatesRepository.GetByIdAsync(taxRateId);

            if (taxRate == null && throwError)
            {
                var text = FormatNotFoundText("tax rate", taxRateId);
                var code = ApplicationErrors.JURISDICTION_TAX_RATE_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return taxRate;
        }

        /// <summary>
        /// Extension method for checking a currency given its id.
        /// </summary>
        /// <param name="currenciesRepository">Repository to check on.</param>
        /// <param name="currencyId">Currency id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The Currency entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<Currency> CheckCurrencyByIdAsync([NotNull] this ICurrenciesRepository currenciesRepository, Guid currencyId, bool throwError = true, bool throwNotFound = true)
        {
            Check.NotNull(currenciesRepository, nameof(currenciesRepository));

            var currency = await currenciesRepository.GetByIdAsync(currencyId);

            if (currency == null && throwError)
            {
                var text = FormatNotFoundText("currency", currencyId);
                var code = ApplicationErrors.CURRENCY_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return currency;
        }

        /// <summary>
        /// Extension method for checking a FormTemplateVersion given its id.
        /// </summary>
        /// <param name="repository">Repository to check on.</param>
        /// <param name="id">Currency id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The Currency entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<FormTemplateVersion> CheckFormTemplateVersionByIdAsync([NotNull] this IFormTemplateVersionsRepository repository, Guid id, bool throwError = true, bool throwNotFound = true)
        {
            Check.NotNull(repository, nameof(repository));

            var entity = await repository.GetByIdAsync(id);

            if (entity == null && throwError)
            {
                var text = FormatNotFoundText(typeof(FormTemplateVersion).Name, id);
                var code = ApplicationErrors.FORMTEMPLATEVERSION_ID_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return entity;
        }

        /// <summary>
        /// Extension method for checking a FormTemplate given its id.
        /// </summary>
        /// <param name="repository">Repository to check on.</param>
        /// <param name="id">FormTemplate id as Guid.</param>
        /// <param name="throwError">Determines whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determines whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The FormTemplate entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        /// <exception cref="BadRequestException">If not found and throwError == false.</exception>
        public static async Task<FormTemplate> CheckFormTemplateByIdAsync([NotNull] this IFormTemplatesRepository repository,
            Guid id,
            bool throwError = true,
            bool throwNotFound = true)
        {
            Check.NotNull(repository, nameof(repository));
            var entity = await repository.GetByIdAsync(id);
            if (entity == null && throwError)
            {
                var text = FormatNotFoundText(typeof(FormTemplate).Name, id);
                var code = ApplicationErrors.FORMTEMPLATE_ID_NOT_FOUND.ToErrorCode();
                throw throwNotFound ? new NotFoundException(code, text) : new BadRequestException(code, text);
            }

            return entity;
        }

        /// <summary>
        /// Extension method for checking a FormDocument given its id.
        /// </summary>
        /// <param name="repository">Repository to check on.</param>
        /// <param name="id">Currency id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The Currency entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<FormDocument> CheckFormDocumentByIdAsync([NotNull] this IFormDocumentsRepository repository, Guid id, bool throwError = true, bool throwNotFound = true)
        {
            Check.NotNull(repository, nameof(repository));

            var entity = await repository.GetByIdAsync(id);

            if (entity == null && throwError)
            {
                var text = FormatNotFoundText(typeof(FormDocument).Name, id);
                var code = ApplicationErrors.FORMTEMPLATEVERSION_ID_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return entity;
        }

        /// <summary>
        /// Extension method for checking an ApplicationUser given its id.
        /// </summary>
        /// <param name="repository">Repository to check on.</param>
        /// <param name="id">Currency id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The ApplicationUser entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<ApplicationUser> CheckUserByIdAsync([NotNull] this IUserRepository repository, Guid id, bool throwError = true, bool throwNotFound = true)
        {
            Check.NotNull(repository, nameof(repository));

            var entity = await repository.FindByUserByPredicateAsync(x => x.Id == id);

            if (entity == null && throwError)
            {
                var text = FormatNotFoundText(typeof(ApplicationUser).Name, id);
                var code = ApplicationErrors.USER_ID_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return entity;
        }

        /// <summary>
        /// Extension method for checking an InboxReadStatus given its id.
        /// </summary>
        /// <param name="inboxReadStatusesRepository">Repository to check on.</param>
        /// <param name="readStatusId">InboxReadStatus id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The InboxReadStatus entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        /// <exception cref="BadRequestException">If not found and throwError == false.</exception>
        public static async Task<InboxReadStatus> CheckInboxReadStatusByIdAsync([NotNull] this IInboxReadStatusesRepository inboxReadStatusesRepository, Guid readStatusId, bool throwError = true, bool throwNotFound = true)
        {
            Check.NotNull(inboxReadStatusesRepository, nameof(inboxReadStatusesRepository));

            var inboxReadStatus = await inboxReadStatusesRepository.GetByIdAsync(readStatusId);

            if (inboxReadStatus == null && throwError)
            {
                var text = FormatNotFoundText("inbox read status", readStatusId);
                var code = ApplicationErrors.INBOX_READ_STATUS_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                    new BadRequestException(code, text);
            }

            return inboxReadStatus;
        }

        /// <summary>
        /// Extension method for checking an InboxOwner given its id.
        /// </summary>
        /// <param name="inboxOwnersRepository">Repository to check on.</param>
        /// <param name="ownerId">InboxOwner id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The InboxOwner entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        /// <exception cref="BadRequestException">If not found and throwError == false.</exception>
        public static async Task<InboxOwner> CheckInboxOwnerByIdAsync([NotNull] this IInboxOwnersRepository inboxOwnersRepository, Guid ownerId, bool throwError = true, bool throwNotFound = true)
        {
            Check.NotNull(inboxOwnersRepository, nameof(inboxOwnersRepository));

            var inboxOwner = await inboxOwnersRepository.GetByIdAsync(ownerId);

            if (inboxOwner == null && throwError)
            {
                var text = FormatNotFoundText("inbox owner", ownerId);
                var code = ApplicationErrors.INBOX_OWNER_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                    new BadRequestException(code, text);
            }

            return inboxOwner;
        }

        /// <summary>
        /// Extension method for checking an InboxReadStatus does not already exist.
        /// </summary>
        /// <param name="inboxReadStatusesRepository">Repository to check on.</param>
        /// <param name="userId">User id.</param>
        /// <param name="inboxId">Inbox id.</param>
        /// <returns>The InboxReadStatus entity if found.</returns>
        /// <exception cref="BadRequestException">If already exists.</exception>
        public static async Task CheckInboxReadStatusDoesNotExistAsync([NotNull] this IInboxReadStatusesRepository inboxReadStatusesRepository, Guid userId, Guid inboxId)
        {
            Check.NotNull(inboxReadStatusesRepository, nameof(inboxReadStatusesRepository));

            var matchingReadStatus = await inboxReadStatusesRepository.FindByConditionAsync(
                user => user.InboxId == inboxId && user.UserId == userId,
                users => users.Take(2));

            var existingReadStatus = matchingReadStatus.SingleOrDefault();

            if (existingReadStatus != null)
            {
                throw new BadRequestException(
                    ApplicationErrors.INBOX_READ_STATUS_ALREADY_EXISTS.ToErrorCode(),
                    "Inbox read status already exists");
            }
        }

        /// <summary>
        /// Extension method for checking if a module is approved and enabled for a legal entity.
        /// </summary>
        /// <param name="legalEntityModulesRepository">The repository to check on.</param>
        /// <param name="legalEntityId">Id of the legal entity.</param>
        /// <param name="moduleId">Id of the module.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        /// <exception cref="BadRequestException">If the module is not approved or enabled for the legal entity.</exception>
        public static async Task CheckModuleEnabledForLegalEntityAsync(this ILegalEntityModulesRepository legalEntityModulesRepository, Guid legalEntityId, Guid moduleId)
        {
            Check.NotNull(legalEntityModulesRepository, nameof(legalEntityModulesRepository));

            var module = await legalEntityModulesRepository.GetQueryable().SingleOrDefaultAsync(lem =>
                lem.LegalEntityId == legalEntityId && lem.ModuleId == moduleId);

            if (module == null)
            {
                throw new BadRequestException(
                    ApplicationErrors.MODULE_NOT_FOUND.ToErrorCode(), "The module is not available for the legal entity.");
            }

            if (!module.IsApproved)
            {
                throw new BadRequestException(
                    ApplicationErrors.MODULE_NOT_APPROVED.ToErrorCode(), "The module is not available for the legal entity.");
            }

            if (module is not { IsEnabled: true })
            {
                throw new BadRequestException(
                    ApplicationErrors.MODULE_NOT_ENABLED.ToErrorCode(), "The module is not available for the legal entity.");
            }
        }

        /// <summary>
        /// Extension method for checking a submission given its id.
        /// </summary>
        /// <param name="repository">Repository to check on.</param>
        /// <param name="submissionId">Submission id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <param name="options">The query options.</param>
        /// <returns>The Submission entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<Submission> CheckSubmissionByIdAsync([NotNull] this IRepository<Submission, Guid> repository, Guid submissionId, bool throwError = true, bool throwNotFound = true, Func<IQueryable<Submission>, IQueryable<Submission>> options = null)
        {
            Check.NotNull(repository, nameof(repository));

            var submission = await repository.GetByIdAsync(submissionId, options);

            if (submission == null && throwError)
            {
                var text = FormatNotFoundText("submission", submissionId);
                var code = ApplicationErrors.SUBMISSION_ID_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return submission;
        }

        /// <summary>
        /// Extension method for checking a Document given its id.
        /// </summary>
        /// <param name="service">Service to check on.</param>
        /// <param name="documentId">Document id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <param name="includeData">Optional bool to denote whether to include the binary data, the default value is false.</param>
        /// <returns>The Document entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<DocumentDTO> CheckDocumentByIdAsync([NotNull] this IDocumentManager service, Guid documentId, bool throwError = true, bool throwNotFound = true, bool includeData = false)
        {
            Check.NotNull(service, nameof(service));

            var document = await service.GetDocumentAsync(documentId, includeData);

            if (document == null && throwError)
            {
                var text = FormatNotFoundText("document", documentId);
                var code = ApplicationErrors.FILE_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return document;
        }

        /// <summary>
        /// Extension method for checking a FormDocumentDocument given its id.
        /// </summary>
        /// <param name="repository">Repository to check on.</param>
        /// <param name="documentId">Document id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <returns>The FormDocumentDocument entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<FormDocumentDocument> CheckFormDocumentDocumentByDocumentIdAsync([NotNull] this IFormDocumentDocumentsRepository repository, Guid documentId, bool throwError = true, bool throwNotFound = true)
        {
            Check.NotNull(repository, nameof(repository));

            var formDocumentDocument = await repository.FindFirstOrDefaultByConditionAsync(fdd => fdd.DocumentId == documentId);

            if (formDocumentDocument == null && throwError)
            {
                var text = FormatNotFoundText("form document document", documentId);
                var code = ApplicationErrors.FILE_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return formDocumentDocument;
        }

        /// <summary>
        /// Extension method for checking a message given its id.
        /// </summary>
        /// <param name="repository">The inox repository.</param>
        /// <param name="messageId">Id of the message to check.</param>
        /// <param name="throwError">Determine if an error should be thrown.</param>
        /// <returns>The message (Inbox) entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<Inbox> CheckInboxMessageAsync([NotNull] this IInboxRepository repository, Guid messageId, bool throwError = true)
        {
            Check.NotNull(repository, nameof(repository));

            var entity = await repository.GetByIdAsync(messageId);

            if (entity == null && throwError)
            {
                var text = FormatNotFoundText("message", messageId);
                var code = ApplicationErrors.INBOXMESSAGE_NOT_FOUND.ToErrorCode();
                throw new NotFoundException(code, text);
            }

            return entity;
        }

        /// <summary>
        /// Extension method for checking an announcement given its id.
        /// </summary>
        /// <param name="repository">The inox repository.</param>
        /// <param name="announcementId">Id of the announcement to check.</param>
        /// <param name="throwError">Determine if an error should be thrown.</param>
        /// <param name="options">Optional options to apply to the query.</param>
        /// <returns>The Announcement entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<Announcement> CheckAnnouncementByIdAsync([NotNull] this IAnnouncementsRepository repository, Guid announcementId, bool throwError = true, Func<IQueryable<Announcement>, IQueryable<Announcement>> options = null)
        {
            Check.NotNull(repository, nameof(repository));

            var entity = await repository.GetByIdAsync(announcementId, options);

            if (entity == null && throwError)
            {
                var text = FormatNotFoundText("announcement", announcementId);
                var code = ApplicationErrors.ANNOUNCEMENT_NOT_FOUND.ToErrorCode();

                throw new NotFoundException(code, text);
            }

            return entity;
        }

        /// <summary>
        /// Extension method for checking an announcement document given its id.
        /// </summary>
        /// <param name="repository">The inox repository.</param>
        /// <param name="announcementDocumentId">Id of the announcementDocument to check.</param>
        /// <param name="throwError">Determine if an error should be thrown.</param>
        /// <param name="options">Optional options to apply to the query.</param>
        /// <returns>The AnnouncementDocument entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<AnnouncementDocument> CheckAnnouncementDocumentByIdAsync([NotNull] this IAnnouncementDocumentsRepository repository, Guid announcementDocumentId, bool throwError = true, Func<IQueryable<AnnouncementDocument>, IQueryable<AnnouncementDocument>> options = null)
        {
            Check.NotNull(repository, nameof(repository));

            var entity = await repository.GetByIdAsync(announcementDocumentId, options);

            if (entity == null && throwError)
            {
                var text = FormatNotFoundText("announcement document", announcementDocumentId);
                var code = ApplicationErrors.ANNOUNCEMENT_DOCUMENT_NOT_FOUND.ToErrorCode();

                throw new NotFoundException(code, text);
            }

            return entity;
        }

        /// <summary>
        /// Extension method for checking a request for information given its id.
        /// </summary>
        /// <param name="repository">Repository to check on.</param>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <param name="options">Optional options to apply to the query.</param>
        /// <returns>The RequestForInformation entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<RequestForInformation> CheckRequestForInformationByIdAsync([NotNull] this IRequestForInformationRepository repository, Guid requestForInformationId, bool throwError = true, bool throwNotFound = true, Func<IQueryable<RequestForInformation>, IQueryable<RequestForInformation>> options = null)
        {
            Check.NotNull(repository, nameof(repository));

            var requestForInformation = await repository.GetByIdAsync(requestForInformationId, options);

            if (requestForInformation == null && throwError)
            {
                var text = FormatNotFoundText("request for information", requestForInformationId);
                var code = ApplicationErrors.REQUEST_FOR_INFORMATION_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return requestForInformation;
        }

        /// <summary>
        /// Extension method for checking a request for information document given its id.
        /// </summary>
        /// <param name="repository">Repository to check on.</param>
        /// <param name="requestForInformationDocumentId">The request for information document id as Guid.</param>
        /// <param name="throwError">Determine whether an error should be thrown.</param>
        /// <param name="throwNotFound">Determine whether the error type should be NotFound instead of BadRequest.</param>
        /// <param name="options">Optional options to apply to the query.</param>
        /// <returns>The RequestForInformationDocument entity if found.</returns>
        /// <exception cref="NotFoundException">If not found and throwError == true.</exception>
        public static async Task<RequestForInformationDocument> CheckRequestForInformationDocumentByIdAsync([NotNull] this IRequestForInformationDocumentsRepository repository, Guid requestForInformationDocumentId, bool throwError = true, bool throwNotFound = true, Func<IQueryable<RequestForInformationDocument>, IQueryable<RequestForInformationDocument>> options = null)
        {
            Check.NotNull(repository, nameof(repository));

            var requestForInformation = await repository.GetByIdAsync(requestForInformationDocumentId, options);

            if (requestForInformation == null && throwError)
            {
                var text = FormatNotFoundText("request for information document", requestForInformationDocumentId);
                var code = ApplicationErrors.REQUEST_FOR_INFORMATION_DOCUMENT_NOT_FOUND.ToErrorCode();

                throw throwNotFound ? new NotFoundException(code, text) :
                                      new BadRequestException(code, text);
            }

            return requestForInformation;
        }

        /// <summary>
        /// Formats the text when an entity is not found.
        /// </summary>
        /// <param name="entityName">Name of the 'not found' entity.</param>
        /// <param name="id">Id of the 'not found' entity.</param>
        /// <returns>The created text.</returns>
        private static string FormatNotFoundText(string entityName, Guid id)
        {
            return FormatNotFoundText(entityName, id.ToString());
        }

        /// <summary>
        /// Formats the text when an entity is not found.
        /// </summary>
        /// <param name="entityName">Name of the 'not found' entity.</param>
        /// <param name="id">Id of the 'not found' entity.</param>
        /// <returns>The created text.</returns>
        private static string FormatNotFoundText(string entityName, string id)
        {
            return $"The {entityName} with id '{id}' was not found";
        }

        /// <summary>
        /// Formats the not found text for the extension methods using a custom property property.
        /// </summary>
        /// <param name="entityName">The entity name as string.</param>
        /// <param name="property">The property name as string.</param>
        /// <param name="keyword">The keyword name as string. The default is 'id'.</param>
        /// <returns>The formatted not found text as string.</returns>
        private static string FormatNotFoundText(string entityName, string property, string keyword = "id")
        {
            return $"The {entityName} with {keyword} '{property}' was not found";
        }
    }
}