﻿// <copyright file="IApplicationUsersRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Services.Identity.EFModels;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.Domain.Users
{
    /// <summary>
    /// Interface for the ApplicationUser repository.
    /// </summary>
    public interface IApplicationUsersRepository : IRepositoryService
    {
        /// <summary>
        /// Gets the DbContext of the repository.
        /// </summary>
        DbContext DbContext { get; }

        /// <summary>
        /// Finds users using predicate and apply paging.
        /// </summary>
        /// <param name="expression">Expression for the search.</param>
        /// <param name="pageNumber">Page number for paging.</param>
        /// <param name="pageSize">Page size for paging.</param>
        /// <param name="options">Callback to modify the IQueryable like includes and ordering.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<IPagedList<ApplicationUser>> FindByConditionAsPagedListAsync(Expression<Func<ApplicationUser, bool>> expression, int pageNumber = 1, int pageSize = int.MaxValue, Func<IQueryable<ApplicationUser>, IQueryable<ApplicationUser>> options = null);

        /// <summary>
        /// Finds users using predicate.
        /// </summary>
        /// <param name="predicate">The predicate to find users.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<List<ApplicationUser>> FindByConditionAsync(Expression<Func<ApplicationUser, bool>> predicate);
    }
}
