﻿namespace NetProGroup.Trust.Tests.MasterClients
{
    [TestFixture()]
    public class MasterClientsDataManagerTests
    {
        [Test()]
        public void SyncMasterClientsAsync_WhenManagementUserExistsForEmail_CreatesClientUser()
        {
            Assert.Fail();
        }

        [Test()]
        public void SyncMasterClientsAsync_WhenClientUserUserExistsForEmail_DoesNotCreateClientUser()
        {
            Assert.Fail();
        }
    }
}