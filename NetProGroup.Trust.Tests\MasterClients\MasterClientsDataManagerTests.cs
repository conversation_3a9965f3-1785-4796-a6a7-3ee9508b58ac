using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.DataManager.LegalEntityRelations.MasterClients.RequestResponses;
using NetProGroup.Trust.DataManager.MasterClients;
using NetProGroup.Trust.Domain.Users;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.MasterClients
{
    [TestFixture]
    public class MasterClientsDataManagerTests : TestBase
    {
        private IMasterClientsDataManager _masterClientsDataManager;
        private IUserManager _userManager;
        private IApplicationUsersRepository _applicationUsersRepository;

        [SetUp]
        public void Setup()
        {
            _masterClientsDataManager = _server.Services.GetRequiredService<IMasterClientsDataManager>();
            _userManager = _server.Services.GetRequiredService<IUserManager>();
            _applicationUsersRepository = _server.Services.GetRequiredService<IApplicationUsersRepository>();
        }

        [Test]
        public async Task SyncMasterClientsAsync_WhenManagementUserExistsForEmail_CreatesClientUser()
        {
            // Arrange
            var testEmail = "<EMAIL>";

            // Create a management user (non-client role)
            var managementUser = await _userManager.CreateUserWithRolesAsync(
                new RegistrationDTO
                {
                    LastName = "Management",
                    FirstName = "Test",
                    UserName = testEmail,
                    DisplayName = "Test Management User",
                    Email = testEmail,
                    ObjectId = Guid.NewGuid(),
                    RoleIds = new List<Guid> { _nevisOwnerRoleId }
                }
            );

            var syncRequest = new SyncMasterClientRequest
            {
                MasterClientUsers = new List<SyncMasterClientUser>
                {
                    new SyncMasterClientUser
                    {
                        UniqueId = "TEST001",
                        MasterClientCode = "MC001",
                        MasterClientName = "Test Master Client",
                        UserEmail = testEmail
                    }
                }
            };

            // Act
            await _masterClientsDataManager.SyncMasterClientsAsync(syncRequest);

            // Assert
            var allUsers = await _applicationUsersRepository.FindByConditionAsync(u => u.Email == testEmail);

            // Should have both the management user and a new client user
            allUsers.Should().HaveCount(2);
            allUsers.Should().Contain(u => u.Id == managementUser.Id);
            allUsers.Should().Contain(u => u.Id != managementUser.Id &&
                u.ApplicationUserRoles.Any(r => r.RoleId == _clientRoleId));
        }

        [Test]
        public async Task SyncMasterClientsAsync_WhenClientUserExistsForEmail_DoesNotCreateClientUser()
        {
            // Arrange
            var testEmail = "<EMAIL>";

            // Create a client user
            var existingClientUser = await _userManager.CreateUserWithRolesAsync(
                new RegistrationDTO
                {
                    LastName = "Client",
                    FirstName = "Test",
                    UserName = testEmail,
                    DisplayName = "Test Client User",
                    Email = testEmail,
                    ObjectId = Guid.NewGuid(),
                    RoleIds = new List<Guid> { _clientRoleId }
                }
            );

            var syncRequest = new SyncMasterClientRequest
            {
                MasterClientUsers = new List<SyncMasterClientUser>
                {
                    new SyncMasterClientUser
                    {
                        UniqueId = "TEST002",
                        MasterClientCode = "MC002",
                        MasterClientName = "Test Master Client 2",
                        UserEmail = testEmail
                    }
                }
            };

            // Act
            await _masterClientsDataManager.SyncMasterClientsAsync(syncRequest);

            // Assert
            var allUsers = await _applicationUsersRepository.FindByConditionAsync(u => u.Email == testEmail);

            // Should still have only the original client user
            allUsers.Should().HaveCount(1);
            allUsers.Single().Id.Should().Be(existingClientUser.Id);
        }
    }
}