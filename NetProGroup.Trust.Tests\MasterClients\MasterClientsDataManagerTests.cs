using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.DataManager.LegalEntityRelations.MasterClients.RequestResponses;
using NetProGroup.Trust.DataManager.MasterClients;
using NetProGroup.Trust.Domain.Users;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.MasterClients
{
    [TestFixture]
    public class MasterClientsDataManagerTests : TestBase
    {
        private IMasterClientsDataManager _masterClientsDataManager;
        private IUserManager _userManager;
        private IApplicationUsersRepository _applicationUsersRepository;

        [SetUp]
        public void Setup()
        {
            _masterClientsDataManager = _server.Services.GetRequiredService<IMasterClientsDataManager>();
            _userManager = _server.Services.GetRequiredService<IUserManager>();
            _applicationUsersRepository = _server.Services.GetRequiredService<IApplicationUsersRepository>();
        }

        [Test]
        public async Task SyncMasterClientsAsync_WhenManagementUserExistsForEmail_CreatesClientUser()
        {
            // Arrange
            var testEmail = "<EMAIL>";
            var managementUser = await CreateTestUserAsync(testEmail, "Management", _nevisOwnerRoleId);
            var syncRequest = CreateSyncRequest(testEmail, "TEST001", "MC001");

            // Act
            await _masterClientsDataManager.SyncMasterClientsAsync(syncRequest);

            // Assert
            var allUsers = await _applicationUsersRepository.FindByConditionAsync(u => u.Email == testEmail);

            // Should have both the management user and a new client user
            allUsers.Should().HaveCount(2);
            allUsers.Should().Contain(u => u.Id == managementUser.Id);

            var newClientUser = allUsers.Single(u => u.Id != managementUser.Id);
            newClientUser.ApplicationUserRoles.Should().Contain(r => r.RoleId == _clientRoleId);
        }

        [Test]
        public async Task SyncMasterClientsAsync_WhenClientUserExistsForEmail_DoesNotCreateClientUser()
        {
            // Arrange
            var testEmail = "<EMAIL>";
            var existingClientUser = await CreateTestUserAsync(testEmail, "Client", _clientRoleId);
            var syncRequest = CreateSyncRequest(testEmail, "TEST002", "MC002");

            // Act
            await _masterClientsDataManager.SyncMasterClientsAsync(syncRequest);

            // Assert
            var allUsers = await _applicationUsersRepository.FindByConditionAsync(u => u.Email == testEmail);

            // Should still have only the original client user
            allUsers.Should().HaveCount(1);
            var user = allUsers.Single();
            user.Id.Should().Be(existingClientUser.Id);
            user.ApplicationUserRoles.Should().Contain(r => r.RoleId == _clientRoleId);
        }

        private async Task<ApplicationUserDTO> CreateTestUserAsync(string email, string userType, Guid roleId)
        {
            return await _userManager.CreateUserWithRolesAsync(
                new RegistrationDTO
                {
                    LastName = userType,
                    FirstName = "Test",
                    UserName = email,
                    DisplayName = $"Test {userType} User",
                    Email = email,
                    ObjectId = Guid.NewGuid(),
                    RoleIds = new List<Guid> { roleId }
                }
            );
        }

        private static SyncMasterClientRequest CreateSyncRequest(string userEmail, string uniqueId, string masterClientCode)
        {
            return new SyncMasterClientRequest
            {
                MasterClientUsers = new List<SyncMasterClientUser>
                {
                    new SyncMasterClientUser
                    {
                        UniqueId = uniqueId,
                        MasterClientCode = masterClientCode,
                        MasterClientName = $"Test Master Client {masterClientCode}",
                        UserEmail = userEmail
                    }
                }
            };
        }
    }
}