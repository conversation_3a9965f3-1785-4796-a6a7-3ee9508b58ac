﻿// <copyright file="ApplicationErrors.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Errors
{
    /// <summary>
    /// Enurmeration of errocodes.
    /// </summary>
    public enum ApplicationErrors
    {
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable CA1707 // Identifiers should not contain underscores
#pragma warning disable SA1602 // Enumeration items should be documented

        // 0000 - 0999 DATA ERRORS
        NONE = 0,
        TEXT_AREA_CONTENT_NOT_ALLOWED = 0001,
        TEXT_FIELD_CONTENT_NOT_ALLOWED = 0002,
        FILE_IS_NOT_IMAGE = 0003,
        FILE_NOT_FOUND = 0004,
        DATE_REQUIRED = 0005,

        FILE_MAX_COUNT_EXCEEDED = 0006,
        UNKNOWN_TYPE = 0007,
        INVALID_SORTBY = 0008,
        DOCUMENT_ID_EMPTY_ARRAY = 0009,

        // 1000 - 1099 JURISDICTION
        JURISDICTION_NOT_FOUND = 1000,

        JURISDICTION_TAX_RATE_NOT_FOUND = 1050,
        JURISDICTION_UNKNOWN = 1051,
        INVALID_JURISDICTION = 1052,

        // 1100 - 1199 MASTERCLIENT
        MASTERCLIENT_NOT_FOUND = 1100,
        MASTERCLIENT_USER_NOT_FOUND = 1105,
        MASTERCLIENT_USER_EXISTS = 1106,
        MASTERCLIENT_USER_IS_NOT_MANUALLY_ADDED = 1107,

        // 1200 - 1299 COMPANY
        COMPANY_NOT_FOUND = 1200,
        COMPANY_NO_INCORPORATIONDATE = 1201,

        // 1500 - 1599 CURRENCIES
        CURRENCY_NOT_FOUND = 1500,

        // 1600 - 1610 MODULES
        MODULE_NOT_FOUND = 1600,
        MODULE_NOT_APPROVED = 1601,
        MODULE_NOT_ENABLED = 1602,

        NOT_SUPPORTED_FOR_MODULE = 1603,
        MULTIPLE_MODULES_NOT_ALLOWED = 1604,
        MODULE_NOT_ALLOWED = 1605,

        // 1611 - 1620 Payments
        PAYMENT_NOT_FOUND = 1611,
        PAYMENT_CANNOT_BE_REPLACED = 1612,
        PAYMENT_TRANSACTION_NOT_FOUND = 1613,
        PAYMENT_TRANSACTION_IN_PROGRESS = 1614,
        PAYMENT_TRANSACTION_HAS_EXPIRED = 1615,
        PAYMENT_TRANSACTION_LOCK = 1616,
        PAYMENT_TRANSACTION_COMPLETED = 1617,

        // 1650 - 1699 Invoices
        INVOICE_NOT_FOUND = 1650,
        INVOICE_NUMBERING_SETTINGS_NOT_FOUND = 1651,
        INVOICE_IS_NOT_SAME_CURRENCY = 1652,
        INVOICE_IS_NOT_SAME_JURISDICTION = 1653,
        INVOICE_IS_IN_PAYMENT_TRANSACTION = 1654,
        INVOICE_IS_ALREADY_PAID = 1655,

        // 2000 - 2199 USER ERRORS
        USER_ID_NOT_FOUND = 2000,
        USER_ID_NOT_IN_HEADER = 2001,
        USER_ALREADY_ADDED = 2002,
        ROLE_NOT_FOUND = 2003,
        USER_EMAIL_NOT_FOUND = 2004,
        USER_NOT_ACTIVE = 2005,

        UNKNOWN_MFA_METHOD = 2100,

        // 2200 - 2299 BO ERRORS
        BENEFICIALOWNER_NOT_FOUND = 2200,
        STATUS_DOES_NOT_ALLOW_UPDATES = 2201,

        // 2300 - 2399 DIR ERRORS
        DIRECTOR_NOT_FOUND = 2300,

        // 2400 - 2499 ROM ERRORS
        SHAREHOLDER_NOT_FOUND = 2400,

        // 3000 - 3049 FORM ERRORS
        FORMTEMPLATE_ID_NOT_FOUND = 3000,
        FORMTEMPLATEVERSION_ID_NOT_FOUND = 3001,
        FORMTEMPLATEVERSION_NOT_FOUND = 3002,

        FORMDOCUMENT_ID_NOT_FOUND = 3010,
        FORMDOCUMENTREVISION_ID_NOT_FOUND = 3011,
        FORMDOCUMENTREVISION_NOT_FOUND = 3012,

        FORMDOCUMENT_ALREADY_FINALIZED = 3013,
        FORMDOCUMENTREVISION_ALREADY_FINALIZED = 3014,

        FORMDOCUMENT_INCORRECT_STATUS = 3015,
        FORMDOCUMENTREVISION_INCORRECT_STATUS = 3016,

        FORMDOCUMENTREVISION_STATUS_DOES_NOT_ALLOW_UPDATES = 3020,
        FORMDOCUMENTDOCUMENT_NOT_FOUND = 3021,

        // 3050 - 3499 SUBMISSION ERRORS
        SUBMISSION_ID_NOT_FOUND = 3050,
        SUBMISSION_ALREADY_SUBMITTED = 3051,
        SUBMISSION_INCORRECT_STATUS = 3052,
        SUBMISSION_FOR_YEAR_EXISTS = 3053,
        SUBMISSION_NO_INVOICE = 3054,
        SUBMISSION_ALREADY_DELETED = 3055,
        SUBMISSION_ALREADY_EXISTS = 3056,
        INVALID_SUBMISSION_DATE = 3057,
        INVALID_SUBMISSION_DATES = 3058,
        SUBMISSION_FINANCIALPERIOD_ENDS_IN_FUTURE = 3060,

        PRIOR_SUBMISSION_NEEDS_AMENDMENTS = 3061,
        SUBMISSION_OPERATION_NOT_ALLOWED = 3062,
        SUBMISSION_IS_LOCKED = 3063,

        // 3500 - 3999 REQUEST FOR INFORMATION ERRORS
        REQUEST_FOR_INFORMATION_NOT_FOUND = 3500,
        REQUEST_FOR_INFORMATION_ALREADY_EXIST = 3501,
        REQUEST_FOR_INFORMATION_DOCUMENT_NOT_FOUND = 3502,
        INVALID_REQUEST_FOR_INFORMATION_STATUS = 3503,
        REQUEST_FOR_INFORMATION_NOTIFICATION_ALREADY_SENT = 3504,
        REQUEST_FOR_INFORMATION_INVALID_REMINDER_TYPE = 3505,

        // 4000 - 4499 INBOX ERRORS
        INBOX_NOT_FOUND = 4000,
        INBOX_OWNER_NOT_FOUND = 4001,
        INBOX_READ_STATUS_NOT_FOUND = 4002,
        INBOX_OWNER_ALREADY_EXISTS = 4003,
        INBOX_READ_STATUS_ALREADY_EXISTS = 4004,
        INBOXMESSAGE_NOT_FOUND = 4005,
        INVALID_INBOX_OWNER = 4006,

        // 4500 - 4999 LEGAL ENTITY ERRORS
        LEGALENTITY_MODULES_NOT_APPROVED = 4500,
        LEGALENTITY_FEE_SETTINGS_NOT_ALLOWED = 4501,
        LEGALENTITY_ANNUALFEE_YEAR_NOT_ALLOWED = 4502,
        LEGALENTITY_ONBOARDINGSTATUS_INVALID = 4503,
        LEGALENTITY_HAS_SUBMISSIONS = 4504,
        LEGALENTITY_HAS_INVOICE = 4505,

        // 5000 - 5999 REPORT ERRORS
        REPORT_NOT_FOUND = 5000,
        NOT_SUPPORTED_FOR_REPORT = 5003,

        // 10000 - 10999 PERMISSIONS ERRORS
        PERMISSION_ID_NOT_FOUND = 10000,
        NO_PERMISSION = 10001,
        INVALID_JURISDICTION_ACCESS = 10002,

        // 11000 - 11099 MESSAGE TEMPLATE ERRORS
        MESSAGETEMPLATE_NOT_FOUND = 11000,

        // 12000 - 12099 AUTH ERRORS
        USER_IS_NOT_MANAGEMENT_USER = 12000,
        USER_IS_NOT_CLIENT_USER = 12001,

        // 13000 - 13099 TECHNICAL ERRORS
        DATABASE_DOES_NOT_EXIST = 13000,

        // 13500 - 13599 ANNOUNCEMENT ERRORS
        ANNOUNCEMENT_NOT_FOUND = 13500,
        ANNOUNCEMENT_RECIPIENTS_NOT_FOUND = 13501,
        ANNOUNCEMENT_DOCUMENT_NOT_FOUND = 13502,
        INVALID_ANNOUNCEMENT_STATUS = 13503,
        ANNOUNCEMENT_RECIPIENTS_MAX_COUNT_EXCEEDED = 13504,

#pragma warning restore SA1602 // Enumeration items should be documented
#pragma warning restore CA1707 // Identifiers should not contain underscores
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
    }
}