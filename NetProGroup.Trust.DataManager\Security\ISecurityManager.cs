﻿// <copyright file="ISecurityManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Trust.Application.Contracts.Jurisdictions;
using NetProGroup.Trust.Domain.Jurisdictions;

namespace NetProGroup.Trust.DataManager.Security
{
    /// <summary>
    /// Interface for SecurityManager that handles user authentication, authorization, and permission management.
    /// </summary>
    public interface ISecurityManager : IScopedService
    {
        /// <summary>
        /// Gets the WorkContext instance.
        /// </summary>
        IWorkContext WorkContext { get; }

        /// <summary>
        /// Gets or sets the current UserId (from WorkContext or custom).
        /// </summary>
        Guid UserId { get; set; }

        /// <summary>
        /// Checks if the context user is valid and throws a ForbiddenException if not.
        /// </summary>
        /// <exception cref="ForbiddenException">The user is unknown.</exception>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireUserAsync();

        /// <summary>
        /// Checks if the context user is valid and throws a ForbiddenException if not.
        /// </summary>
        /// <exception cref="ForbiddenException">The user is unknown.</exception>
        void RequireUser();

        /// <summary>
        /// Checks it the user has the given application role and throws an exception if not.
        /// </summary>
        /// <param name="role">The required role.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireApplicationRoleAsync(string role);

        /// <summary>
        /// Checks if the user has one of the given application role and throws an exception if not.
        /// </summary>
        /// <param name="roles">The list of roles that the user should have one matching.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireOneOfApplicationRolesAsync(params string[] roles);

        /// <summary>
        /// Checks it the user has the given application role and returns true if so.
        /// </summary>
        /// <param name="role">The required role.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<bool> UserHasApplicationRoleAsync(string role);

        /// <summary>
        /// Checks it the user has one of the given application role and returns true if so.
        /// </summary>
        /// <param name="roles">The list of roles that the user should have one matching.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<bool> UserHasOneOfApplicationRolesAsync(params string[] roles);

        /// <summary>
        /// Checks it the user has one of the given application role and returns true if so.
        /// </summary>
        /// <param name="roles">The list of roles that the user should have one matching.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<bool> UserHasOneOfApplicationRolesAsync(ICollection<string> roles);

        /// <summary>
        /// Checks if the user has the given permission.
        /// </summary>
        /// <param name="permission">The permisison to check.</param>
        /// <returns>True if user has the permission.</returns>
        Task<bool> HasManagementPermissionAsync(string permission);

        /// <summary>
        /// Checks if the user has one of the given permissions.
        /// </summary>
        /// <param name="permissions">The permisisons to check.</param>
        /// <returns>True if user has the permission.</returns>
        Task<bool> HasOneOfPermissionsAsync(string[] permissions);

        /// <summary>
        /// Checks if the user has the given permission and throws an exception if not.
        /// </summary>
        /// <param name="permission">The permisison to check.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireManagementPermissionAsync(string permission);

        /// <summary>
        /// Checks if the user has one of the given permissions and throws an exception if not.
        /// </summary>
        /// <param name="permissions">The permisisons to check.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireOneOfPermissionsAsync(params string[] permissions);

        /// <summary>
        /// Gets a dictionary with per permission the list of roles that have that permission.
        /// </summary>
        /// <returns>A dictionary with per permission the list of roles that have that permission.</returns>
        Dictionary<string, IList<string>> GetPermissionsWithRoles();

        /// <summary>
        /// Checks it the user has access to the given company.
        /// </summary>
        /// <param name="legalEntityId">The id of the company to check access for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<bool> ClientUserHasAccessToCompanyAsync(Guid legalEntityId);

        /// <summary>
        /// Checks if the user has access to the company and throws an exception if not.
        /// </summary>
        /// <param name="legalEntityId">The id of the company to check.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireClientAccessToCompanyAsync(Guid legalEntityId);

        /// <summary>
        /// Checks it the user has access to the given masterclient.
        /// </summary>
        /// <param name="masterClientId">The id of the masterclient to check access for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<bool> ClientUserHasAccessToMasterClientAsync(Guid masterClientId);

        /// <summary>
        /// Checks it the user has access to the given jurisdiction.
        /// </summary>
        /// <param name="jurisdictionId">The id of the jurisdiction to check access for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<bool> ManagementUserHasAccessToJurisdictionAsync(Guid jurisdictionId);

        /// <summary>
        /// Checks if the user has access to the masterclient and throws an exception if not.
        /// </summary>
        /// <param name="masterClientId">The id of the masterclient to check.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireClientAccessToMasterClientAsync(Guid masterClientId);

        /// <summary>
        /// Gets the ids of the jurisdictions that the user has the given permission for.
        /// </summary>
        /// <remarks>
        /// You can use this for example to check in which jurisdictions a company search can be performed by the user.
        /// </remarks>
        /// <param name="permission">The permission to get the jurisdictions for.</param>
        /// <returns>A list of jurisdiction ids.</returns>
        Task<List<Guid>> GetJurisdictionsForManagementPermissionAsync(string permission);

        /// <summary>
        /// Checks if the user has the given permission and throws an exception if not.
        /// </summary>
        /// <param name="permission">The permission to check.</param>
        /// <param name="legalEntityId">The id of the legal entity to check the permission for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireManagementPermissionForLegalEntityAsync(string permission, Guid legalEntityId);

        /// <summary>
        /// Checks if the user has the given permission and throws an exception if not.
        /// </summary>
        /// <param name="permission">The permission to check.</param>
        /// <param name="requestForInformationId">The id of the legal entity to check the permission for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireManagementPermissionForRequestForInformationAsync(string permission, Guid requestForInformationId);

        /// <summary>
        /// Checks if the user is a client.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<bool> UserIsClient();

        /// <summary>
        /// Checks if the user is a client and throws an exception if not.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        /// <exception cref="ForbiddenException">Thrown when the user is not a management portal user.</exception>
        Task RequireManagementUserAsync();

        /// <summary>
        /// Checks if the user is a management portal user and has access to the given company.
        /// </summary>
        /// <param name="legalEntityId">The id of the company to check access for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireManagementAccessToCompanyAsync(Guid legalEntityId);

        /// <summary>
        /// Checks if the user has management access to the specified jurisdiction.
        /// </summary>
        /// <param name="jurisdictionId">The ID of the jurisdiction to check access for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireManagementAccessToJurisdictionAsync(Guid jurisdictionId);

        /// <summary>
        /// Checks if the user is a client user and throws an exception if not.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        /// <exception cref="ForbiddenException">Thrown when the user is not a client user.</exception>
        Task RequireClientUserAsync();

        /// <summary>
        /// Gets the ids of the jurisdictions that the user has any permission for.
        /// </summary>
        /// <remarks>
        /// You can use this for example to check in which jurisdictions a company search can be performed by the user.
        /// </remarks>
        /// <returns>A list of jurisdiction ids and names.</returns>
        Task<List<JurisdictionDTO>> GetAllJurisdictionsForManagementPermissionsAsync();

        /// <summary>
        /// Checks if the user has the specified permission for a jurisdiction.
        /// </summary>
        /// <param name="permission">The permission to check.</param>
        /// <param name="jurisdictionId">The ID of the jurisdiction to check the permission for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireManagementPermissionForJurisdictionAsync(string permission, Guid jurisdictionId);

        /// <summary>
        /// Checks if the user has the specified permission for a submission.
        /// </summary>
        /// <param name="permission">The permission to check.</param>
        /// <param name="submissionId">The ID of the submission to check the permission for.</param>
        /// <param name="allowDeleted">The allowDeleted is set to include the deleted submissions.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireManagementPermissionForSubmissionAsync(string permission, Guid submissionId, bool allowDeleted = false);

        /// <summary>
        /// Checks if the user has client access to the specified submission.
        /// </summary>
        /// <param name="submissionId">The ID of the submission to check access for.</param>
        /// <param name="allowDeleted">The allowDeleted is set to include the deleted submissions.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireClientAccessToSubmissionAsync(Guid submissionId, bool allowDeleted = false);

        /// <summary>
        /// Requires the user to have the correct permission for the given entity.
        /// </summary>
        /// <param name="entityName">The EntityName from the Audit log.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RequireManagementPermissionForAuditEntityAsync(string entityName);

        /// <summary>
        /// Validates the jurisdictions access for the current user.
        /// </summary>
        /// <param name="jurisdictionIds">The unique identifiers of the jurisdictions to validate access.</param>
        /// <returns>The result of the task.</returns>
        Task ValidateJurisdictionsAccess(IEnumerable<Guid> jurisdictionIds);
    }
}
