﻿// <copyright file="LegalEntity.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Services.EFAuditing;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Settings;
using NetProGroup.Trust.Domain.Shared.Enums;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Domain.LegalEntities
{
    /// <summary>
    /// Represents a LegalEntity entity in the database.
    /// </summary>
    public class LegalEntity : StampedEntity<Guid>, IAuditableEntity
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="LegalEntity"/> class.
        /// </summary>
        public LegalEntity() : this(isActive: true)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="LegalEntity"/> class.
        /// </summary>
        /// <param name="onboardingStatus">The onboarding status of the entity.</param>
        /// <param name="isActive">Whether the entity should be created as active.</param>
        public LegalEntity(OnboardingStatus onboardingStatus = OnboardingStatus.Onboarding, bool isActive = true)
        {
            IsActive = isActive;
            OnboardingStatus = onboardingStatus;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="LegalEntity"/> class.
        /// </summary>
        /// <param name="id">Id of the entity to get.</param>
        /// <param name="onboardingStatus">The onboarding status of the entity.</param>
        /// <param name="isActive">Whether the entity should be created as active.</param>
        public LegalEntity(Guid id, OnboardingStatus onboardingStatus = OnboardingStatus.Onboarding, bool isActive = true)
           : base(id)
        {
            IsActive = isActive;
            OnboardingStatus = onboardingStatus;
        }

        /// <summary>
        /// Gets a value indicating whether the entity is active.
        /// </summary>
        public bool IsActive { get; private set; }

        /// <summary>
        /// Gets the date when the entity was set to inactive.
        /// </summary>
        public DateTime? InactiveSetAt { get; private set; }

        /// <summary>
        /// Gets or sets the type of legal entity (like 'Company').
        /// </summary>
        public LegalEntityType EntityType { get; set; }

        /// <summary>
        /// Gets or sets the unique external id of the entity.
        /// </summary>
        public string ExternalUniqueId { get; set; }

        /// <summary>
        /// Gets or sets the name of the entity.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the code of the entity (aka VG Code). This is unique within a jurisdiction.
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets the legacy code where applicable.
        /// </summary>
        public string LegacyCode { get; set; }

        /// <summary>
        /// Gets or sets the name of the type of the entity/company.
        /// </summary>
        /// <remarks>
        /// BC Company, Limited Partnership, Trust, Fund, Vessel, Foundation.
        /// </remarks>
        public string EntityTypeName { get; set; }

        /// <summary>
        /// Gets or sets the code of the type of the entity/company.
        /// </summary>
        public string EntityTypeCode { get; set; }

        /// <summary>
        /// Gets or sets the incorporation number.
        /// </summary>
        public string IncorporationNr { get; set; }

        /// <summary>
        /// Gets or sets the incorporation date.
        /// </summary>
        public DateTime? IncorporationDate { get; set; }

        /// <summary>
        /// Gets or sets the Jurisdiction of Registration.
        /// </summary>
        public string JurisdictionOfRegistration { get; set; }

        /// <summary>
        /// Gets or sets the referral office.
        /// </summary>
        public string ReferralOffice { get; set; }

        /// <summary>
        /// Gets or sets the production office (formerly known as service office).
        /// </summary>
        public string ProductionOffice { get; set; }

        /// <summary>
        /// Gets or sets the entity status.
        /// </summary>
        public string EntityStatus { get; set; }

        /// <summary>
        /// Gets or sets the entity sub-status.
        /// </summary>
        public string EntitySubStatus { get; set; }

        /// <summary>
        /// Gets or sets the risk group.
        /// </summary>
        public string RiskGroup { get; set; }

        /// <summary>
        /// Gets or sets the master Administrator.
        /// </summary>
        public string Administrator { get; set; }

        /// <summary>
        /// Gets or sets the Manager.
        /// </summary>
        public string Manager { get; set; }

        /// <summary>
        /// Gets the onboarding status.
        /// </summary>
        public OnboardingStatus OnboardingStatus { get; private set; }

        /// <summary>
        /// Gets or sets the master client code.
        /// </summary>
        public string MasterClientCode { get; set; }

        /// <summary>
        /// Gets or sets the id of the MasterClient.
        /// </summary>
        public Guid MasterClientId { get; set; }

        /// <summary>
        /// Gets or sets the MasterClient.
        /// </summary>
        /// <value>The <see cref="MasterClient"/>.</value>
        public virtual MasterClient MasterClient { get; set; }

        /// <summary>
        /// Gets or sets the id of the Jurisdiction. Temporarily optional but must be required.
        /// </summary>
        public Guid? JurisdictionId { get; set; }

        /// <summary>
        /// Gets or sets the Jurisdiction.
        /// </summary>
        /// <value>The <see cref="Jurisdiction"/>.</value>
        public virtual Jurisdiction Jurisdiction { get; set; }

        /// <summary>
        /// Gets the collection of LegalEntityModules.
        /// </summary>
        /// <value>A collection of <see cref="LegalEntityModule"/>.</value>
        public virtual ICollection<LegalEntityModule> LegalEntityModules { get; } = new HashSet<LegalEntityModule>();

        /// <summary>
        /// Gets the collection of Submissions associated with this LegalEntity.
        /// </summary>
        /// <value>A collection of <see cref="Submission"/>.</value>
        public virtual ICollection<Submission> Submissions { get; } = new HashSet<Submission>();

        /// <summary>
        /// Gets or sets the collection of settings associated with this LegalEntity.
        /// </summary>
        public virtual ICollection<Setting> Settings { get; set; } = new HashSet<Setting>();

        /// <summary>
        /// Gets or sets the collection of annual fee statuses.
        /// </summary>
        public virtual ICollection<LegalEntityAnnualFee> AnnualFees { get; set; } = new HashSet<LegalEntityAnnualFee>();

        /// <summary>
        /// Gets or sets the collection of Directors.
        /// </summary>
        public virtual ICollection<Director> Directors { get; set; }

        /// <summary>
        /// Gets or sets the collection of Beneficial Owners.
        /// </summary>
        public virtual ICollection<BeneficialOwner> BeneficialOwners { get; set; }

        /// <summary>
        /// Updates the annual fee status for a specific financial year.
        /// </summary>
        /// <param name="financialYear">The financial year for which to update the status.</param>
        /// <param name="isPaid">Whether the annual fee is paid or not.</param>
        /// <returns>The updated or newly created <see cref="LegalEntityAnnualFee"/> instance.</returns>
        public LegalEntityAnnualFee UpdateAnnualFeeStatus(int financialYear, bool isPaid)
        {
            var annualFeeStatus = AnnualFees.FirstOrDefault(afs => afs.FinancialYear == financialYear);
            if (annualFeeStatus == null)
            {
                annualFeeStatus = new LegalEntityAnnualFee(financialYear, isPaid);
                AnnualFees.Add(annualFeeStatus);
            }
            else
            {
                annualFeeStatus.IsPaid = isPaid;
            }

            return annualFeeStatus;
        }

        /// <summary>
        /// Sets the active status of the legal entity.
        /// </summary>
        /// <param name="companyDeletedAt">The date when the company was deleted, if setting to inactive.</param>
        public void SetInactive(DateTime? companyDeletedAt = null)
        {
            IsActive = false;

            InactiveSetAt = companyDeletedAt ?? DateTime.UtcNow;
        }

        /// <summary>
        /// Reactivates the legal entity if it is currently inactive and approved.
        /// </summary>
        /// <exception cref="InvalidOperationException">If the legal entity is already active or not approved.</exception>
        public void Reactivate()
        {
            if (IsActive)
            {
                throw new InvalidOperationException("Cannot reactivate an already active legal entity.");
            }

            if (OnboardingStatus != OnboardingStatus.Approved)
            {
                throw new InvalidOperationException(
                    "Cannot reactivate a legal entity that is not approved. For other states, use ResetToOnboarding and let the user approve the onboarding, which will set IsActive to true.");
            }

            SetActive();
        }

        /// <summary>
        /// Set onboarding status to approved and IsActive to true.
        /// </summary>
        public void ApproveOnboarding()
        {
            OnboardingStatus = OnboardingStatus.Approved;
            IsActive = true;
        }

        /// <summary>
        /// Set onboarding status to declined and IsActive to false.
        /// </summary>
        public void DeclineOnboarding()
        {
            OnboardingStatus = OnboardingStatus.Declined;
            SetInactive();
        }

        /// <summary>
        /// Checks if the annual fee for a specific financial year is paid.
        /// </summary>
        /// <param name="financialYear">The financial year to check.</param>
        /// <returns>True if the annual fee is paid; otherwise, false.</returns>
        public bool AnnualFeePaid(int financialYear)
        {
            return AnnualFees
                .Any(annualFee => annualFee.FinancialYear == financialYear && annualFee.IsPaid);
        }

        /// <summary>
        /// Resets the legal entity to the onboarding phase.
        /// </summary>
        /// <exception cref="InvalidOperationException">If the LegalEntity is currently active.</exception>
        public void ResetToOnboarding()
        {
            if (IsActive == true)
            {
                throw new InvalidOperationException("Cannot reset to onboarding if the legal entity is active.");
            }

            OnboardingStatus = OnboardingStatus.Onboarding;
        }

        /// <summary>
        /// Closes the legal entity while it is still in the onboarding phase.
        /// </summary>
        /// <exception cref="InvalidOperationException">When the LegalEntity is not in the onboarding phase or is active.</exception>
        public void CloseWhileOnboarding()
        {
            if (OnboardingStatus != OnboardingStatus.Onboarding || IsActive != false)
            {
                throw new InvalidOperationException($"Invalid state transition: entity is not in onboarding state or is active. Current status: {OnboardingStatus}, IsActive: {IsActive}.");
            }

            OnboardingStatus = OnboardingStatus.ClosedWhileOnboarding;
        }

        /// <summary>
        /// Resets the onboarding status to 'Onboarding' from 'Approved'.
        /// </summary>
        /// <exception cref="InvalidOperationException">When the LegalEntity is not approved.</exception>
        public void ResetToOnboardingFromManagementPortal()
        {
            if (OnboardingStatus != OnboardingStatus.Approved)
            {
                throw new InvalidOperationException("Can only reset to onboarding from approved status.");
            }

            OnboardingStatus = OnboardingStatus.Onboarding;
            SetInactive();
        }

        /// <summary>
        /// Sets the legal entity as active.
        /// </summary>
        private void SetActive()
        {
            if (IsActive)
            {
                throw new InvalidOperationException("Cannot set an already active legal entity as active.");
            }

            IsActive = true;
        }
    }
}
