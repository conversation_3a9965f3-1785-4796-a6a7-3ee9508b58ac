namespace NetProGroup.Trust.DataMigration
{
    /// <summary>
    /// Represents the application settings for data migration.
    /// </summary>
    public class DataMigrationAppSettings
    {
        /// <summary>
        /// Gets or sets the interval for updating progress during entity migration.
        /// </summary>
        public int ProgressUpdateInterval { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to use a dummy initial sync.
        /// WARNING: This should never be set to true in production environments!
        /// </summary>
        public bool UseDummyInitialSync { get; set; }

        /// <summary>
        /// Gets or sets the date limit for using new branding.
        /// </summary>
        public DateTime UseNewBrandingLimitDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the data migration process is enabled.
        /// </summary>
        public bool Enabled { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the information about unprocessed records should be stored.
        /// </summary>
        public bool StoreUnprocessedRecords { get; set; }

        /// <summary>
        /// The amount of seconds of job lock expiration to keep as a margin before refreshing
        /// </summary>
        public int JobLockRefreshMarginSeconds { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to ignore payment data when no invoice number is stored on the submission.
        /// </summary>
        public bool IgnorePaymentWhenNoInvoiceNumber { get; set; }

        /// <summary>
        /// Gets or sets a dictionary of country name overrides for mapping countries during form generation.
        /// Key is the original country name, value is what it should be mapped to.
        /// </summary>
        public Dictionary<string, string> CountryOverrides { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// Gets or sets the active jurisdiction code for the data migration process.
        /// </summary>
        public string ActiveJurisdiction { get; set; }

        /// <summary>
        /// Gets or sets the jurisdiction-specific settings for data migration, keyed by jurisdiction code.
        /// </summary>
        public Dictionary<string, JurisdictionSettings> Jurisdictions { get; set; }
    }

    /// <summary>
    /// Represents the settings specific to a jurisdiction for data migration operations.
    /// </summary>
    public class JurisdictionSettings
    {
        /// <summary>
        /// Gets or sets the name of the MongoDB database for the jurisdiction.
        /// </summary>
        public string MongoDatabaseName { get; set; }

        /// <summary>
        /// Gets or sets the MongoDB connection string for the jurisdiction.
        /// </summary>
        public string MongoConnectionString { get; set; }

        /// <summary>
        /// Storage accounts required for the migration.
        /// </summary>
        public List<Framework.Services.Documents.StorageAccount> StorageAccounts { get; set; }
    }
}
