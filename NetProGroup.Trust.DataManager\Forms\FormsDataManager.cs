﻿// <copyright file="FormsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Forms;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Forms.RequestResponses;
using NetProGroup.Trust.DataManager.Helpers;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Forms;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Forms
{
    /// <summary>
    /// Manager for forms data.
    /// </summary>
    public class FormsDataManager : IFormsDataManager
    {
        private readonly IMapper _mapper;
        private readonly IWorkContext _workContext;

        private readonly IFormTemplatesRepository _formTemplatesRepository;
        private readonly IFormTemplateVersionsRepository _formTemplateVersionsRepository;
        private readonly IFormDocumentsRepository _formDocumentsRepository;
        private readonly IFormDocumentRevisionsRepository _formDocumentRevisionsRepository;
        private readonly IFormDocumentAttributesRepository _formDocumentAttributesRepository;

        private readonly IModulesRepository _modulesRepository;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;

        private readonly IAuthorizationFilterExpressionFactory _authorizationFilterExpressionFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="FormsDataManager"/> class.
        /// </summary>
        /// <param name="mapper">Mapper instance.</param>
        /// <param name="workContext">Instance of the current WorkContext.</param>
        /// <param name="formTemplatesRepository">Instance of repository for FormTemplates.</param>
        /// <param name="formTemplateVersionsRepository">Instance of repository for FormTemplateVersions.</param>
        /// <param name="formDocumentsRepository">Instance of repository for FormDocuments.</param>
        /// <param name="formDocumentRevisionsRepository">Instance of repository for FormDocumentRevisions.</param>
        /// <param name="formDocumentAttributesRepository">Instance of repository for ForDocumentAttributes.</param>
        /// <param name="modulesRepository">Instance of repository for Modules.</param>
        /// <param name="jurisdictionsRepository">Instance of repository for Jurisdictions.</param>
        /// <param name="authorizationFilterExpressionFactory">Instance of the authorization filter expression factory.</param>
        public FormsDataManager(IMapper mapper,
                                        IWorkContext workContext,
                                        IFormTemplatesRepository formTemplatesRepository,
                                        IFormTemplateVersionsRepository formTemplateVersionsRepository,
                                        IFormDocumentsRepository formDocumentsRepository,
                                        IFormDocumentRevisionsRepository formDocumentRevisionsRepository,
                                        IFormDocumentAttributesRepository formDocumentAttributesRepository,
                                        IModulesRepository modulesRepository,
                                        IJurisdictionsRepository jurisdictionsRepository,
                                        IAuthorizationFilterExpressionFactory authorizationFilterExpressionFactory)
        {
            _mapper = mapper;
            _workContext = workContext;

            _formTemplatesRepository = formTemplatesRepository;
            _formTemplateVersionsRepository = formTemplateVersionsRepository;
            _formDocumentsRepository = formDocumentsRepository;
            _formDocumentRevisionsRepository = formDocumentRevisionsRepository;
            _formDocumentAttributesRepository = formDocumentAttributesRepository;

            _modulesRepository = modulesRepository;
            _jurisdictionsRepository = jurisdictionsRepository;
            _authorizationFilterExpressionFactory = authorizationFilterExpressionFactory;
        }

        /// <inheritdoc/>
        public async Task<GetFormDocumentResponse> GetFormDocumentAsync(GetFormDocumentRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.FormDocumentId, nameof(request.FormDocumentId));

            if (request.Revision.HasValue && request.Revision.Value != 0)
            {
                Check.Positive(request.Revision.Value, nameof(request.Revision));
            }

            var formDocument = await _formDocumentsRepository.GetByIdAsync(request.FormDocumentId);
            if (formDocument == null)
            {
                var text = TextHelpers.FormatNotFoundText("formdocument", request.FormDocumentId);
                var code = ApplicationErrors.FORMDOCUMENT_ID_NOT_FOUND.ToErrorCode();
                throw new NotFoundException(code, text);
            }

            var documentRevisions = new List<FormDocumentRevision>();

            // Version specified?
            if (request.Revision.HasValue)
            {
                // Get the exact version
                documentRevisions.Add(await _formDocumentRevisionsRepository.FindFirstOrDefaultByConditionAsync(x =>
                    x.FormDocumentId == formDocument.Id && x.Revision == request.Revision));

                if (documentRevisions.Count == 0)
                {
                    var text = $"Revision '{request.Revision}' for document '{formDocument.Name}' is not found";
                    var code = ApplicationErrors.FORMDOCUMENTREVISION_NOT_FOUND.ToErrorCode();
                    throw new NotFoundException(code, text);
                }
            }
            else if (request.GetAllRevisions)
            {
                documentRevisions.AddRange(await _formDocumentRevisionsRepository.FindByConditionAsync(x =>
                    x.FormDocumentId == formDocument.Id,
                    q => q.OrderByDescending(ft => ft.Revision)));

                if (documentRevisions.Count == 0)
                {
                    var text = $"No revisions found for document '{formDocument.Name}'";
                    var code = ApplicationErrors.FORMDOCUMENTREVISION_NOT_FOUND.ToErrorCode();
                    throw new NotFoundException(code, text);
                }
            }
            else
            {
                // Get latest revision
                documentRevisions.Add(await _formDocumentRevisionsRepository.FindFirstOrDefaultByConditionAsync(x =>
                    x.FormDocumentId == formDocument.Id,
                    q => q.OrderByDescending(ft => ft.Revision)));

                if (documentRevisions.Count == 0)
                {
                    var text = $"A revision for document '{formDocument.Name}' is not found";
                    var code = ApplicationErrors.FORMDOCUMENTREVISION_NOT_FOUND.ToErrorCode();
                    throw new NotFoundException(code, text);
                }
            }

            var result = new GetFormDocumentResponse();

            result.FormDocument = _mapper.Map<FormDocumentWithRevisionsDTO>(formDocument);
            result.FormDocument.Revisions = _mapper.Map<List<FormDocumentRevisionDTO>>(documentRevisions);

            return result;
        }

        /// <inheritdoc />
        public async Task<FormTemplateDTO> CheckFormTemplateByIdAsync(Guid formTemplateId)
        {
            var formTemplate = await _formTemplatesRepository.CheckFormTemplateByIdAsync(formTemplateId);

            return _mapper.Map<FormTemplateDTO>(formTemplate);
        }

        /// <inheritdoc/>
        public async Task<GetFormTemplateResponse> GetFormTemplateAsync(GetFormTemplateRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.FormTemplateId, nameof(request.FormTemplateId));

            var formTemplate = await _formTemplatesRepository.GetByIdAsync(request.FormTemplateId);
            if (formTemplate == null)
            {
                var text = TextHelpers.FormatNotFoundText("formtemplate", request.FormTemplateId);
                var code = ApplicationErrors.FORMTEMPLATE_ID_NOT_FOUND.ToErrorCode();
                throw new NotFoundException(code, text);
            }

            var templateVersions = new List<FormTemplateVersion>();
            bool allowIncludeForm = false;

            // Version specified?
            if (request.FormTemplateVersionId.HasValue)
            {
                // Get the exact version
                templateVersions.Add(await _formTemplateVersionsRepository.FindFirstOrDefaultByConditionAsync(x =>
                    x.FormTemplateId == formTemplate.Id && x.Id == request.FormTemplateVersionId));

                if (templateVersions.Count == 0)
                {
                    var text = $"Version '{request.FormTemplateVersionId}' for template '{formTemplate.Name}' is not found";
                    var code = ApplicationErrors.FORMTEMPLATEVERSION_NOT_FOUND.ToErrorCode();
                    throw new NotFoundException(code, text);
                }

                allowIncludeForm = true;
            }
            else if (!string.IsNullOrEmpty(request.Version))
            {
                // Get the exact version
                templateVersions.Add(await _formTemplateVersionsRepository.FindFirstOrDefaultByConditionAsync(x =>
                    x.FormTemplateId == formTemplate.Id && x.Version == request.Version));

                if (templateVersions.Count == 0)
                {
                    var text = $"Version '{request.Version}' for template '{formTemplate.Name}' is not found";
                    var code = ApplicationErrors.FORMTEMPLATEVERSION_NOT_FOUND.ToErrorCode();
                    throw new NotFoundException(code, text);
                }

                allowIncludeForm = true;
            }
            else if (request.CheckDate.HasValue)
            {
                // Get the version on or after this date
                templateVersions.Add(await _formTemplateVersionsRepository.FindFirstOrDefaultByConditionAsync(x =>
                    x.FormTemplateId == formTemplate.Id && x.StartAt >= request.CheckDate,
                    q => q.OrderBy(ft => ft.StartAt).Take(1)));

                if (templateVersions.Count == 0)
                {
                    var text = $"A version on or after '{request.CheckDate.Value.ToString("yyyy-MM-dd")}' for template '{formTemplate.Name}' is not found";
                    var code = ApplicationErrors.FORMTEMPLATEVERSION_NOT_FOUND.ToErrorCode();
                    throw new NotFoundException(code, text);
                }

                allowIncludeForm = true;
            }
            else if (request.GetAllVersions)
            {
                templateVersions.AddRange(await _formTemplateVersionsRepository.FindByConditionAsync(x =>
                    x.FormTemplateId == formTemplate.Id,
                    q => q.OrderByDescending(ft => ft.StartAt)));

                if (templateVersions.Count == 0)
                {
                    var text = $"No versions found for template '{formTemplate.Name}'";
                    var code = ApplicationErrors.FORMTEMPLATEVERSION_NOT_FOUND.ToErrorCode();
                    throw new NotFoundException(code, text);
                }
            }
            else
            {
                // Get latest version
                templateVersions.Add(await _formTemplateVersionsRepository.FindFirstOrDefaultByConditionAsync(x =>
                    x.FormTemplateId == formTemplate.Id,
                    q => q.OrderByDescending(ft => ft.StartAt).ThenByDescending(ft => ft.Year).ThenByDescending(ft => ft.CreatedAt)));

                if (templateVersions.Count == 0)
                {
                    var text = $"A version for template '{formTemplate.Name}' is not found";
                    var code = ApplicationErrors.FORMTEMPLATEVERSION_NOT_FOUND.ToErrorCode();
                    throw new NotFoundException(code, text);
                }

                allowIncludeForm = true;
            }

            var result = new GetFormTemplateResponse();

            result.FormTemplate = _mapper.Map<FormTemplateWithVersionsDTO>(formTemplate);
            result.FormTemplate.Versions = _mapper.Map<List<FormTemplateVersionDTO>>(templateVersions);

            if (allowIncludeForm && request.IncludeForm)
            {
                result.FormTemplate.Versions.First().FormBuilder = FormBuilder.FromJson(templateVersions.First().DataAsJson);
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task<ListFormDocumentsResponse> ListFormDocumentsAsync(ListFormDocumentsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.LegalEntityId, nameof(request.LegalEntityId));

            var result = new ListFormDocumentsResponse();

            var pagedData = await _formDocumentsRepository.FindByConditionAsPagedListAsync(x =>
                        x.LegalEntityId == request.LegalEntityId,
                        pageNumber: request.PageNumber,
                        pageSize: request.PageSize,
                        options: q => q.OrderBy(x => x.Name));

            var subset = _mapper.Map<List<FormDocumentDTO>>(pagedData);

            result.FormDocumentItems = new StaticPagedList<FormDocumentDTO>(subset, pagedData.GetMetaData());

            return result;
        }

        /// <inheritdoc/>
        public async Task<ListFormTemplatesResponse> ListFormTemplatesAsync(ListFormTemplatesRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.JurisdictionId, nameof(request.JurisdictionId));

            var result = new ListFormTemplatesResponse();

            Expression<Func<FormTemplate, bool>> predicate = x =>
                x.JurisdictionId == request.JurisdictionId && (x.ModuleId == request.Moduleid || request.Moduleid == null);

            var pagedData = await _formTemplatesRepository.FindByConditionAsPagedListAsync(predicate,
                        pageNumber: request.PageNumber,
                        pageSize: request.PageSize,
                        options: q => q.OrderBy(ft => ft.Name)
                                       .Include(ft => ft.Jurisdiction)
                                       .Include(ft => ft.Module));

            var subset = _mapper.Map<List<FormTemplateDTO>>(pagedData);

            result.FormTemplateItems = new StaticPagedList<FormTemplateDTO>(subset, pagedData.GetMetaData());

            return result;
        }

        /// <inheritdoc/>
        public async Task<GetFormDocumentResponse> CreateFormDocumentAsync(CreateFormDocumentRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.FormTemplateVersionId, nameof(request.FormTemplateVersionId));
            Check.NotDefaultOrNull<Guid>(request.LegalEntityId, nameof(request.LegalEntityId));

            // Get the template
            var formTemplateVersion = await _formTemplateVersionsRepository.CheckFormTemplateVersionByIdAsync(request.FormTemplateVersionId);

            // Use template if no form in request
            var form = request.Form;
            if (form == null)
            {
                form = Trust.Forms.FormBuilder.FromJson(formTemplateVersion.DataAsJson).Form;
            }

            // Create the document
            var newFormDocument = new FormDocument
            {
                LegalEntityId = request.LegalEntityId,
                FormTemplateVersionId = formTemplateVersion.Id,
                Name = string.IsNullOrEmpty(request.Name) ? formTemplateVersion.Name : request.Name,

                Status = DomainShared.Enums.FormDocumentStatus.Draft,
            };

            // Add the initial revision
            newFormDocument.FormDocumentRevisions.Add(new FormDocumentRevision
            {
                Revision = 0,    // means that this is the initial document
                DataAsJson = form.ToJson(),

                Status = DomainShared.Enums.FormDocumentRevisionStatus.Draft,
            });

            // ToDo: Log activity
            //
            var formDocument = await _formDocumentsRepository.InsertAsync(newFormDocument, saveChanges: true);

            var result = new GetFormDocumentResponse();

            result.FormDocument = _mapper.Map<FormDocumentWithRevisionsDTO>(formDocument);
            result.FormDocument.Revisions = _mapper.Map<List<FormDocumentRevisionDTO>>(formDocument.FormDocumentRevisions);

            return result;
        }

        /// <inheritdoc/>
        public async Task<FormTemplateWithVersionsDTO> CreateFormTemplateVersionAsync(CreateFormTemplateVersionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));
            Check.NotDefaultOrNull<Guid>(model.FormTemplateId, nameof(model.FormTemplateId));

            var formTemplate = await _formTemplatesRepository.GetByIdAsync(model.FormTemplateId);
            if (formTemplate == null)
            {
                var text = TextHelpers.FormatNotFoundText("formtemplate", model.FormTemplateId);
                var code = ApplicationErrors.FORMTEMPLATE_ID_NOT_FOUND.ToErrorCode();
                throw new NotFoundException(code, text);
            }

            // Creat a version
            var version = new FormTemplateVersion
            {
                Name = model.Name,
                StartAt = model.StartDate,
                Version = model.Version,
                Year = model.Year,
            };
            formTemplate.FormTemplateVersions.Add(version);

            // Did the caller provide a formbuilder?
            if (model.FormBuilder != null)
            {
                version.DataAsJson = model.FormBuilder.ToJson();
            }
            else
            {
                // Get the current last version
                var lastVersion = await _formTemplateVersionsRepository.FindFirstOrDefaultByConditionAsync(x =>
                    x.FormTemplateId == formTemplate.Id,
                    q => q.OrderByDescending(ft => ft.StartAt).ThenByDescending(ft => ft.Year).ThenByDescending(ft => ft.CreatedAt));

                if (lastVersion != null)
                {
                    version.DataAsJson = lastVersion.DataAsJson;
                }
            }

            await _formTemplatesRepository.SaveChangesAsync();

            return (await GetFormTemplateAsync(new GetFormTemplateRequest { FormTemplateId = model.FormTemplateId, FormTemplateVersionId = version.Id })).FormTemplate;
        }

        /// <inheritdoc/>
        public async Task<FormTemplateWithVersionsDTO> UpdateFormTemplateVersionAsync(UpdateFormTemplateVersionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));
            Check.NotDefaultOrNull<Guid>(model.Id, nameof(model.Id));

            var formTemplateVersion = await _formTemplateVersionsRepository.GetByIdAsync(model.Id);
            if (formTemplateVersion == null)
            {
                var text = TextHelpers.FormatNotFoundText("formtemplateversion", model.Id);
                var code = ApplicationErrors.FORMTEMPLATEVERSION_ID_NOT_FOUND.ToErrorCode();
                throw new NotFoundException(code, text);
            }

            formTemplateVersion.Name = model.Name;
            formTemplateVersion.StartAt = model.StartDate;
            formTemplateVersion.Version = model.Version;
            formTemplateVersion.Year = model.Year;

            if (model.FormBuilder != null)
            {
                formTemplateVersion.DataAsJson = model.FormBuilder.ToJson();
            }

            await _formTemplatesRepository.SaveChangesAsync();

            return (await GetFormTemplateAsync(new GetFormTemplateRequest { FormTemplateId = formTemplateVersion.FormTemplateId, FormTemplateVersionId = formTemplateVersion.Id })).FormTemplate;
        }

        /// <inheritdoc />
        public async Task CreateFormTemplatesForJurisdictionAsync(Guid jurisdictionId, string moduleKey, params int[] years)
        {
            Check.NotNullOrWhiteSpace(moduleKey, nameof(moduleKey));
            Check.NotNull(years, nameof(years));

            var module = await _modulesRepository.FindFirstOrDefaultByConditionAsync(x => x.Key == moduleKey);
            var jurisdiction = await _jurisdictionsRepository.CheckJurisdictionByIdAsync(jurisdictionId);

            var formTemplate = await _formTemplatesRepository.FindFirstOrDefaultByConditionAsync(ft => ft.JurisdictionId == jurisdictionId && ft.ModuleId == module.Id, q => q.Include(ft => ft.FormTemplateVersions));
            if (formTemplate == null)
            {
                formTemplate = new FormTemplate(Guid.NewGuid())
                {
                    JurisdictionId = jurisdictionId,
                    ModuleId = module.Id,
                    Key = moduleKey,
                    Name = module.Name + " template",
                };
                await _formTemplatesRepository.InsertAsync(formTemplate, false);
            }

            foreach (var year in years)
            {
                var formTemplateVersion = formTemplate.FormTemplateVersions.FirstOrDefault(ftv => ftv.Year == year);
                string version = year < 2022 ? "1.0" : "2.0";
                if (formTemplateVersion == null)
                {
                    formTemplateVersion = new FormTemplateVersion { Name = $"{formTemplate.Name} {year.ToString()}", Version = version, Year = year, StartAt = null };
                    formTemplate.FormTemplateVersions.Add(formTemplateVersion);
                }
                else
                {
                    formTemplateVersion.Name = $"{formTemplate.Name} {year.ToString()}";
                    formTemplateVersion.Version = version;
                    formTemplateVersion.Year = year;
                    formTemplateVersion.StartAt = null;
                }

                var sampleKeyValueForm = new NetProGroup.Trust.Forms.Forms.KeyValueForm();
                sampleKeyValueForm.Id = $"{moduleKey.ToLower()}.{year}";
                sampleKeyValueForm.Name = $"{moduleKey}.{year}";
                sampleKeyValueForm.Version = "1";
                sampleKeyValueForm.CreatedAt = DateTime.UtcNow;
                sampleKeyValueForm.CreatedBy = "Seeding";
                sampleKeyValueForm.Description = $"Template for module {moduleKey}, year {year} (jurisdiction {jurisdiction.Name})";

                var bldr = new FormBuilder();
                bldr.Form = sampleKeyValueForm;
                formTemplateVersion.DataAsJson = bldr.ToJson();
            }

            await _formTemplatesRepository.UpdateAsync(formTemplate, true);
        }
    }
}
