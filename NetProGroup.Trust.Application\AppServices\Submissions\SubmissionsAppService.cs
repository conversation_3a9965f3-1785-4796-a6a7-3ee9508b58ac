﻿// <copyright file="SubmissionsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Documents;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.Application.Contracts.Tools;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Modules;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Permissions;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.Submissions
{
    /// <summary>
    /// Application service for submissions.
    /// </summary>
    public class SubmissionsAppService : ISubmissionsAppService
    {
        private readonly IWorkContext _workContext;
        private readonly IDocumentManager _documentManager;
        private readonly ISubmissionsManager _submissionsManager;
        private readonly IModulesDataManager _modulesDataManager;
        private readonly ILegalEntityModulesRepository _legalEntityModulesRepository;
        private readonly ISecurityManager _securityManager;
        private readonly IMapper _mapper;
        private readonly IZipFileGenerator _zipFileGenerator;
        private readonly ILockManager _lockManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsAppService"/> class.
        /// </summary>
        /// <param name="workContext">Instance of the current WorkContext.</param>
        /// <param name="documentManager">An instance of IDocumentManager.</param>
        /// <param name="legalEntityModulesRepository">The repository to use for legal entity modules.</param>
        /// <param name="submissionsManager">The manager to use for submissions.</param>
        /// <param name="modulesDataManager">The manager to use for modules.</param>
        /// <param name="securityManager">The security manager.</param>
        /// <param name="mapper">An instance of the mapper class.</param>
        /// <param name="zipFileGenerator">An instance of the zip file generator service.</param>
        /// <param name="lockManager">The manager for exclusive locks.</param>
        public SubmissionsAppService(IWorkContext workContext,
                                     IDocumentManager documentManager,
                                     ISubmissionsManager submissionsManager,
                                     IModulesDataManager modulesDataManager,
                                     ILegalEntityModulesRepository legalEntityModulesRepository,
                                     ISecurityManager securityManager,
                                     IMapper mapper,
                                     IZipFileGenerator zipFileGenerator,
                                     ILockManager lockManager)
        {
            _workContext = workContext;

            _documentManager = documentManager;
            _submissionsManager = submissionsManager;
            _modulesDataManager = modulesDataManager;
            _legalEntityModulesRepository = legalEntityModulesRepository;
            _securityManager = securityManager;
            _mapper = mapper;
            _zipFileGenerator = zipFileGenerator;
            _lockManager = lockManager;
        }

        /// <inheritdoc/>
        public async Task<SubmissionsPaidStatusResponse> GetSubmissionsPaidStatusByCompanyAndYearAsync(IEnumerable<CompanyFinancialYearDto> companyYearPairs, Guid moduleId)
        {
            ArgumentNullException.ThrowIfNull(companyYearPairs, nameof(companyYearPairs));

            // Authorization
            var moduleKey = (await _modulesDataManager.GetModuleByIdAsync(moduleId)).Key;

            var requiredPermission = moduleKey switch
            {
                ModuleKeyConsts.SimplifiedTaxReturn => WellKnownPermissionNames.STRModule_Submissions_View_Paid,
                ModuleKeyConsts.EconomicSubstanceBahamas => WellKnownPermissionNames.ESBahamasModule_Submissions_View_Paid,
                _ => throw new PreconditionFailedException(ApplicationErrors.NOT_SUPPORTED_FOR_MODULE.ToErrorCode(),
                    $"Mark as paid of submissions not supported for module '{moduleKey}'")
            };

            var authorizedJurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(requiredPermission);

            return await _submissionsManager.GetSubmissionsPaidStatusByCompanyAndYearAsync(companyYearPairs, moduleId, authorizedJurisdictionIDs);
        }

        /// <inheritdoc/>
        public async Task<MarkSubmissionsAsPaidResponse> MarkSubmissionsAsPaidAsync(IEnumerable<Guid> submissionIds, bool isPaid)
        {
            ArgumentNullException.ThrowIfNull(submissionIds, nameof(submissionIds));
            var submissionIdsAsList = submissionIds.ToList();
            Check.NotNullOrEmpty(submissionIdsAsList, nameof(submissionIds));

            // Authorization
            var submissionId = submissionIdsAsList.First();
            var moduleKey = await _submissionsManager.GetModuleKeyForSubmissionAsync(submissionId);

            var requiredPermission = moduleKey switch
            {
                ModuleKeyConsts.SimplifiedTaxReturn => WellKnownPermissionNames.STRModule_Submissions_Mark_Paid,
                ModuleKeyConsts.EconomicSubstanceBahamas => WellKnownPermissionNames.ESBahamasModule_Submissions_Mark_Paid,
                _ => throw new PreconditionFailedException(ApplicationErrors.NOT_SUPPORTED_FOR_MODULE.ToErrorCode(),
                    $"Mark as paid of submissions not supported for module '{moduleKey}'")
            };

            var authorizedJurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(requiredPermission);

            return await _submissionsManager.MarkSubmissionsAsPaidAsync(submissionIdsAsList, isPaid, authorizedJurisdictionIDs);
        }

        /// <inheritdoc/>
        public async Task<MarkSubmissionsAsPaidByCompanyYearResponse> MarkSubmissionsAsPaidByCompanyAndYearAsync(
            IEnumerable<CompanyFinancialYearDto> companyYearPairs, bool isPaid, Guid moduleId)
        {
            ArgumentNullException.ThrowIfNull(companyYearPairs, nameof(companyYearPairs));

            // Authorization
            var moduleKey = (await _modulesDataManager.GetModuleByIdAsync(moduleId)).Key;

            var requiredPermission = moduleKey switch
            {
                ModuleKeyConsts.SimplifiedTaxReturn => WellKnownPermissionNames.STRModule_Payments_Import,
                _ => throw new PreconditionFailedException(ApplicationErrors.NOT_SUPPORTED_FOR_MODULE.ToErrorCode(),
                    $"Mark as paid of submissions not supported for module '{moduleKey}'")
            };

            var authorizedJurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(requiredPermission);

            return await _submissionsManager.MarkSubmissionsAsPaidByCompanyAndYearAsync(companyYearPairs, isPaid, moduleId, authorizedJurisdictionIDs);
        }

        /// <inheritdoc/>
        public async Task<SubmissionDTO> GetSubmissionAsync(Guid submissionId, bool includeFormDocument, bool allowDeleted = false)
        {
            bool isManagementUser = !await _securityManager.UserIsClient();

            if (isManagementUser)
            {
                var moduleKey = await _submissionsManager.GetModuleKeyForSubmissionAsync(submissionId);

                var requiredPermission = moduleKey switch
                {
                    ModuleKeyConsts.SimplifiedTaxReturn => WellKnownPermissionNames.STRModule_Submissions_View,
                    ModuleKeyConsts.EconomicSubstanceBahamas => WellKnownPermissionNames.ESBahamasModule_Submissions_View,
                    _ => throw new PreconditionFailedException(ApplicationErrors.NOT_SUPPORTED_FOR_MODULE.ToErrorCode(), $"GetSubmissionAsync not supported for module '{moduleKey}'")
                };

                await _securityManager.RequireManagementPermissionForSubmissionAsync(requiredPermission, submissionId, allowDeleted);
            }
            else
            {
                await _securityManager.RequireClientAccessToSubmissionAsync(submissionId, allowDeleted);
            }

            return await _submissionsManager.GetSubmissionAsync(submissionId, includeFormDocument, allowDeleted);
        }

        /// <inheritdoc/>
        public async Task<SubmissionDTO> ReopenSubmissionAsync(ReopenSubmissionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            var moduleKey = await GetSubmissionModuleKeyAsync(model.SubmissionId);

            var requiredPermission = moduleKey switch
            {
                ModuleKeyConsts.SimplifiedTaxReturn => WellKnownPermissionNames.STRModule_Submissions_Reset,
                ModuleKeyConsts.BasicFinancialReportPanama => WellKnownPermissionNames.BFRPanamaModule_Submissions_Reset,
                ModuleKeyConsts.EconomicSubstanceBahamas => WellKnownPermissionNames.ESBahamasModule_Submissions_Reset,
                _ => throw new NetProGroup.Framework.Exceptions.PreconditionFailedException(ApplicationErrors.NOT_SUPPORTED_FOR_MODULE.ToErrorCode(), $"Reopening submission not supported for module '{moduleKey}'")
            };

            await _securityManager.RequireManagementPermissionForSubmissionAsync(requiredPermission, model.SubmissionId);

            return await _submissionsManager.ReopenSubmissionAsync(model);
        }

        /// <inheritdoc/>
        public async Task<SubmissionDTO> StartSubmissionAsync(StartSubmissionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));
            var legalEntityId = Check.NotDefaultOrNull<Guid>(model.LegalEntityId, nameof(model));

            await _securityManager.RequireClientAccessToCompanyAsync(legalEntityId);

            // Get exclusive lock to avoid being submitted twice in parallel. Use new id for the IdentityUserId to avoid getting existing lock.
            var exclusiveLock = await _lockManager.AcquireLockAsync(new Framework.Services.Locks.Models.AcquireLockRequestDTO { EntityName = nameof(LegalEntity), EntityId = model.LegalEntityId, IdentityUserId = Guid.NewGuid() });

            if (!exclusiveLock.Id.HasValue)
            {
                throw new PreconditionFailedException(ApplicationErrors.SUBMISSION_IS_LOCKED.ToErrorCode(), "There is a lock on this submission");
            }

            try
            {
                await CheckModuleEnabledAsync(legalEntityId, model.ModuleId);

                model.UserId = _workContext.IdentityUserId.Value;
                model.UserEmail = _workContext.User.Email;

                return await _submissionsManager.StartSubmissionAsync(model);
            }
            finally
            {
                await _lockManager.ReleaseLockAsync(exclusiveLock);
            }
        }

        /// <inheritdoc/>
        public async Task<SubmissionDTO> SubmitSubmissionAsync(SubmitSubmissionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            await _securityManager.RequireClientAccessToSubmissionAsync(model.SubmissionId);

            // Get exclusive lock to avoid being submitted twice in parallel. Use new id for the IdentityUserId to avoid getting existing lock.
            var exclusiveLock = await _lockManager.AcquireLockAsync(new Framework.Services.Locks.Models.AcquireLockRequestDTO { EntityName = "Submission", EntityId = model.SubmissionId, IdentityUserId = Guid.NewGuid() });
            if (!exclusiveLock.Id.HasValue)
            {
                throw new PreconditionFailedException(ApplicationErrors.SUBMISSION_IS_LOCKED.ToErrorCode(), "There is a lock on this submission");
            }

            try
            {
                return await _submissionsManager.SubmitSubmissionAsync(model);
            }
            finally
            {
                await _lockManager.ReleaseLockAsync(exclusiveLock);
            }
        }

        /// <inheritdoc/>
        public async Task<SubmissionDTO> UpdateSubmissionDataSetAsync(SubmissionDataSetDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            await _securityManager.RequireClientAccessToSubmissionAsync(model.Id);

            return await _submissionsManager.UpdateSubmissionDataSetAsync(model);
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListSubmissionDTO>> ListSubmissionsAsync(Guid legalEntityId, Guid moduleId, ListSubmissionsRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            await _securityManager.RequireClientAccessToCompanyAsync(legalEntityId);

            return await _submissionsManager.ListSubmissionsAsync(new ListSubmissionsRequest
            {
                LegalEntityId = legalEntityId,
                ModuleId = moduleId,
                PagingInfo = request.ToPagingInfo(),
                SortingInfo = request.ToSortingInfo()
            });
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListSubmissionDTO>> ListSubmissionsAsync(Guid masterClientId, ListSubmissionsByMasterClientRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            await _securityManager.RequireClientAccessToMasterClientAsync(masterClientId);

            return await _submissionsManager.ListSubmissionsAsync(new ListSubmissionsByMasterClientRequest
            {
                MasterClientId = masterClientId,
                FinancialYears = request.FinancialYears,
                LegalEntityIds = request.LegalEntityIds,
                HasInvoice = request.HasInvoice,
                IsPaid = request.IsPaid,
                SubmissionStatuses = request.SubmissionStatuses,
                PagingInfo = request.ToPagingInfo()
            });
        }

        /// <inheritdoc/>
        public async Task<AvailableSubmissionYearsDTO> GetAvailableSubmissionYears(Guid legalEntityId, Guid moduleId)
        {
            await _securityManager.RequireClientAccessToCompanyAsync(legalEntityId);

            var request = new AvailableSubmissionYearsRequest { LegalEntityId = legalEntityId, ModuleId = moduleId };

            return await _submissionsManager.GetAvailableSubmissionYears(request);
        }

        /// <inheritdoc/>
        public async Task<AllSubmissionYearsDTO> GetAllSubmissionYears(Guid moduleId)
        {
            await _securityManager.RequireManagementUserAsync();

            var request = new AllSubmissionYearsRequest { ModuleId = moduleId };

            return await _submissionsManager.GetAllSubmissionYears(request);
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListSubmissionDTO>> SearchSubmissionsAsync(SearchSubmissionsRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.ModuleId, nameof(request.ModuleId));

            var moduleKey = (await _modulesDataManager.GetModuleByIdAsync(request.ModuleId)).Key;

            var requiredPermission = moduleKey switch
            {
                ModuleKeyConsts.SimplifiedTaxReturn => WellKnownPermissionNames.STRModule_Submissions_Search,
                ModuleKeyConsts.EconomicSubstanceBVI => WellKnownPermissionNames.ESBVIModule_Submissions_Search,
                _ => throw new PreconditionFailedException(ApplicationErrors.NOT_SUPPORTED_FOR_MODULE.ToErrorCode(), $"Search of submissions not supported for module '{moduleKey}'")
            };

            var authorizedJurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(requiredPermission);

            return await _submissionsManager.SearchSubmissionsAsync(new SearchSubmissionsRequest
            {
                ModuleId = request.ModuleId,
                PagingInfo = request.ToPagingInfo(),
                SortingInfo = request.ToSortingInfo(),
                FinancialYear = request.FinancialYear,
                LegalEntitySearchTerm = request.LegalEntitySearchTerm,
                MasterClientSearchTerm = request.MasterClientSearchTerm,
                ReferralOfficeSearchTerm = request.ReferralOfficeSearchTerm,
                GeneralSearchTerm = request.GeneralSearchTerm,
                SubmittedAfterDate = request.SubmittedAfterDate,
                SubmittedBeforeDate = request.SubmittedBeforeDate,
                IsPaid = request.IsPaid,
                IsExported = request.IsExported,
                AuthorizedJurisdictionIDs = authorizedJurisdictionIDs,
                Country = request.Country,
                IsDeleted = request.IsDeleted
            });
        }

        /// <inheritdoc/>
        public async Task DeleteSubmissionAsync(Guid submissionId)
        {
            // Authorization
            await _securityManager.RequireClientAccessToSubmissionAsync(submissionId);

            // Delete the submission
            await _submissionsManager.DeleteSubmissionAsync(submissionId);
        }

        /// <inheritdoc/>
        public async Task UpdateSubmissionGeneralInformationAsync(Guid submissionId, UpdateSubmissionInformationDTO data)
        {
            // Check the authenticated user
            await _securityManager.RequireClientAccessToSubmissionAsync(submissionId);

            // Update the submission's general information
            await _submissionsManager.UpdateSubmissionGeneralInformationAsync(submissionId, data, true);
        }

        /// <inheritdoc/>
        public async Task UpdateSubmissionGeneralInformationManagementAsync(Guid submissionId, UpdateSubmissionInformationDTO data)
        {
            // Check the authenticated user
            await _securityManager.RequireManagementUserAsync();

            // Update the submission's general information
            await _submissionsManager.UpdateSubmissionGeneralInformationManagementAsync(submissionId, data, true);
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListSubmissionDTO>> SearchSubmissionsForPanamaAsync(FilterSubmissionsRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.ModuleId, nameof(request.ModuleId));

            var moduleKey = (await _modulesDataManager.GetModuleByIdAsync(request.ModuleId)).Key;

            var requiredPermission = moduleKey switch
            {
                ModuleKeyConsts.BasicFinancialReportPanama => WellKnownPermissionNames.BFRPanamaModule_Submissions_Search,
                _ => throw new PreconditionFailedException(ApplicationErrors.NOT_SUPPORTED_FOR_MODULE.ToErrorCode(), $"Search of submissions not supported for module '{moduleKey}'")
            };

            var authorizedJurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(requiredPermission);

            var filterSubmissionsRequest = _mapper.Map<FilterSubmissionsRequest>(request);
            filterSubmissionsRequest.AuthorizedJurisdictionIDs = authorizedJurisdictionIDs;

            return await _submissionsManager.SearchSubmissionsForPanamaAsync(filterSubmissionsRequest);
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListSubmissionBahamasDTO>> SearchSubmissionsForBahamasAsync(FilterSubmissionsRequestForBahamasDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.ModuleId, nameof(request.ModuleId));

            var moduleKey = (await _modulesDataManager.GetModuleByIdAsync(request.ModuleId)).Key;

            var requiredPermission = moduleKey switch
            {
                ModuleKeyConsts.EconomicSubstanceBahamas => WellKnownPermissionNames.ESBahamasModule_Submissions_Search,
                _ => throw new PreconditionFailedException(ApplicationErrors.NOT_SUPPORTED_FOR_MODULE.ToErrorCode(), $"Search of submissions not supported for module '{moduleKey}'")
            };

            var authorizedJurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(requiredPermission);

            var filterSubmissionsRequest = _mapper.Map<FilterSubmissionsRequestForBahamas>(request);
            filterSubmissionsRequest.AuthorizedJurisdictionIDs = authorizedJurisdictionIDs;

            return await _submissionsManager.SearchSubmissionsForBahamasAsync(filterSubmissionsRequest);
        }

        /// <inheritdoc/>
        public async Task<ZipFileDTO> RetrieveSubmissionDocumentsAsync(Guid submissionId)
        {
            await _securityManager.RequireManagementUserAsync();

            // Retrieve the submission entity
            var submission = await _submissionsManager.GetSubmissionAsync(submissionId, true);

            // Generate the zip file if any document exists
            if (submission.DocumentIds.Count > 0)
            {
                var result = await _zipFileGenerator.GenerateZipFileAsync(submission.DocumentIds);
                result.FileName = $"{submission.Name}-{DateTime.UtcNow.Date.ToString("yyyy-MM-dd")}.zip";

                return result;
            }

            return null;
        }

        /// <inheritdoc/>
        public async Task<ZipFileDTO> RetrieveSubmissionsDocumentsAsync(List<Guid> submissionIds)
        {
            await _securityManager.RequireManagementUserAsync();

            // Retrieve the submission entity
            var submissions = await _submissionsManager.GetSubmissionsAsync(submissionIds);

            // Generate the zip file if any document exists
            if (submissions.Count > 0)
            {
                var result = await _zipFileGenerator.GenerateZipFileAsync(submissions);
                result.FileName = $"Submission files export-{DateTime.UtcNow.Date.ToString("yyyy-MM-dd")}.zip";

                return result;
            }

            return null;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListSubmissionDTO>> SearchSubmissionsForBVIAsync(SearchSubmissionsRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.ModuleId, nameof(request.ModuleId));

            var moduleKey = (await _modulesDataManager.GetModuleByIdAsync(request.ModuleId)).Key;

            var requiredPermission = moduleKey switch
            {
                ModuleKeyConsts.EconomicSubstanceBVI => WellKnownPermissionNames.ESBVIModule_Submissions_Search,
                _ => throw new PreconditionFailedException(ApplicationErrors.NOT_SUPPORTED_FOR_MODULE.ToErrorCode(), $"Search of submissions not supported for module '{moduleKey}'")
            };

            var authorizedJurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(requiredPermission);

            return await _submissionsManager.SearchSubmissionsAsync(new SearchSubmissionsRequest
            {
                ModuleId = request.ModuleId,
                PagingInfo = request.ToPagingInfo(),
                SortingInfo = request.ToSortingInfo(),
                FinancialYear = request.FinancialYear,
                LegalEntitySearchTerm = request.LegalEntitySearchTerm,
                MasterClientSearchTerm = request.MasterClientSearchTerm,
                ReferralOfficeSearchTerm = request.ReferralOfficeSearchTerm,
                GeneralSearchTerm = request.GeneralSearchTerm,
                SubmittedAfterDate = request.SubmittedAfterDate,
                SubmittedBeforeDate = request.SubmittedBeforeDate,
                IsPaid = request.IsPaid,
                IsExported = request.IsExported,
                AuthorizedJurisdictionIDs = authorizedJurisdictionIDs,
                Country = request.Country
            });
        }

        private async Task CheckModuleEnabledAsync(Guid legalEntityId, Guid moduleId)
        {
            await _legalEntityModulesRepository.CheckModuleEnabledForLegalEntityAsync(legalEntityId, moduleId);
        }

        /// <summary>
        /// Gets the key of the module for the submission.
        /// </summary>
        /// <param name="submissionId">The submission ID to get the module key for.</param>
        /// <returns>The key of the module of an empty string if no module.</returns>
        private async Task<string> GetSubmissionModuleKeyAsync(Guid submissionId)
        {
            var result = await _modulesDataManager.GetModuleBySubmissionIdAsync(submissionId);
            return result.Key;
        }
    }
}
